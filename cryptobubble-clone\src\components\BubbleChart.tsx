"use client";

import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { CryptoCurrency, BubbleData } from "@/types";

interface BubbleChartProps {
  data: CryptoCurrency[];
  width?: number;
  height?: number;
  onBubbleHover?: (crypto: CryptoCurrency | null) => void;
}

export const BubbleChart: React.FC<BubbleChartProps> = ({
  data,
  width = 1200,
  height = 800,
  onBubbleHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(
    null,
  );

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // Create scales
    const maxMarketCap = d3.max(data, (d) => d.marketCap) || 1;
    const minMarketCap = d3.min(data, (d) => d.marketCap) || 1;

    // Calculate optimal bubble sizes to fill screen without overlapping
    const totalArea = width * height;
    const bubbleCount = data.length;
    const averageArea = (totalArea / bubbleCount) * 0.5; // Use 50% of available space for non-overlapping
    const averageRadius = Math.sqrt(averageArea / Math.PI);

    // Radius scale - conservative sizing to prevent overlap
    const radiusScale = d3
      .scaleSqrt()
      .domain([minMarketCap, maxMarketCap])
      .range([averageRadius * 0.4, averageRadius * 1.8]); // More conservative sizing

    // Color scale for price changes with more vibrant colors
    const colorScale = d3
      .scaleLinear<string>()
      .domain([-15, -5, 0, 5, 15])
      .range(["#dc2626", "#f87171", "#6b7280", "#34d399", "#059669"])
      .clamp(true);

    // Prepare bubble data
    const bubbleData: BubbleData[] = data.map((crypto) => ({
      ...crypto,
      x: 0,
      y: 0,
      r: radiusScale(crypto.marketCap),
      color: colorScale(crypto.change24h),
    }));

    // Create force simulation for non-overlapping bubbles like CryptoBubbles
    const simulation = d3
      .forceSimulation<BubbleData>(bubbleData)
      .force("charge", d3.forceManyBody().strength(-50)) // Moderate repulsion to prevent overlap
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force(
        "collision",
        d3
          .forceCollide<BubbleData>()
          .radius((d) => d.r + 3) // Small padding to prevent overlap
          .strength(1) // Strong collision detection
          .iterations(3),
      ) // Multiple iterations for better collision resolution
      .force("x", d3.forceX<BubbleData>(width / 2).strength(0.05)) // Gentle centering
      .force("y", d3.forceY<BubbleData>(height / 2).strength(0.05))
      // Add boundary forces to keep bubbles on screen
      .force("boundary", () => {
        bubbleData.forEach((d) => {
          const padding = d.r + 5;
          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));
          d.y = Math.max(
            padding,
            Math.min(height - padding, d.y || height / 2),
          );
        });
      })
      .alphaDecay(0.005) // Very slow decay for thorough settling
      .velocityDecay(0.6); // Higher friction for stability

    // Create container group
    const container = svg.append("g");

    // Create bubbles with entrance animation
    const bubbles = container
      .selectAll(".bubble")
      .data(bubbleData)
      .enter()
      .append("g")
      .attr("class", "bubble")
      .style("cursor", "grab")
      .style("opacity", 0);

    // Add circles with gradient effects and inner glow
    const defs = svg.append("defs");

    // Create inner glow gradients for each bubble
    bubbleData.forEach((d, i) => {
      const glowGradient = defs
        .append("radialGradient")
        .attr("id", `inner-glow-${i}`)
        .attr("cx", "50%")
        .attr("cy", "50%")
        .attr("r", "50%");

      const baseColor = d3.color(d.color);

      // Transparent center
      glowGradient
        .append("stop")
        .attr("offset", "0%")
        .attr("stop-color", "transparent")
        .attr("stop-opacity", "0");

      // Start glow at 70% radius
      glowGradient
        .append("stop")
        .attr("offset", "70%")
        .attr("stop-color", "transparent")
        .attr("stop-opacity", "0");

      // Strong glow near the edge
      glowGradient
        .append("stop")
        .attr("offset", "85%")
        .attr("stop-color", baseColor?.brighter(0.5)?.toString() || d.color)
        .attr("stop-opacity", "0.6");

      // Intense glow at the edge
      glowGradient
        .append("stop")
        .attr("offset", "95%")
        .attr("stop-color", baseColor?.brighter(1)?.toString() || d.color)
        .attr("stop-opacity", "0.8");

      // Edge
      glowGradient
        .append("stop")
        .attr("offset", "100%")
        .attr("stop-color", baseColor?.brighter(1.5)?.toString() || d.color)
        .attr("stop-opacity", "1");
    });

    // Create drop shadow filter
    const shadowFilter = defs
      .append("filter")
      .attr("id", "drop-shadow")
      .attr("x", "-50%")
      .attr("y", "-50%")
      .attr("width", "200%")
      .attr("height", "200%");

    shadowFilter
      .append("feDropShadow")
      .attr("dx", "2")
      .attr("dy", "2")
      .attr("stdDeviation", "4")
      .attr("flood-color", "rgba(0,0,0,0.4)");

    // Add inner glow fill circle
    bubbles
      .append("circle")
      .attr("class", "inner-glow")
      .attr("r", 0)
      .attr("fill", (d, i) => `url(#inner-glow-${i})`)
      .attr("stroke", "none")
      .attr("opacity", 1)
      .attr("pointer-events", "none")
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr("r", (d) => d.r);

    // Add invisible hover area (larger than visible stroke)
    bubbles
      .append("circle")
      .attr("class", "hover-area")
      .attr("r", 0)
      .attr("fill", "transparent")
      .attr("stroke", "none")
      .attr("opacity", 0)
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr("r", (d) => d.r)
      .on("end", function () {
        if (this.parentNode) {
          d3.select(this.parentNode as Element).style("opacity", 1);
        }
      });

    // Main stroke outline
    bubbles
      .append("circle")
      .attr("class", "visible-stroke")
      .attr("r", 0)
      .attr("fill", "none")
      .attr("stroke", (d) => {
        const baseColor = d3.color(d.color);
        return baseColor?.brighter(0.5)?.toString() || d.color;
      })
      .attr("stroke-width", 3)
      .attr("stroke-opacity", 0.9)
      .attr("opacity", 1)
      .attr("pointer-events", "none")
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr("r", (d) => d.r);

    // Create improved drag behavior that doesn't interfere with hover
    let isDragging = false;
    let dragStarted = false;

    const dragBehavior = d3
      .drag<SVGGElement, BubbleData>()
      .filter(function(event) {
        // Only allow drag on left mouse button
        return event.button === 0;
      })
      .clickDistance(8) // Require 8 pixels of movement before drag starts
      .on("start", function (event, d) {
        dragStarted = true;
        isDragging = false; // Will be set to true in drag event

        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on("drag", function (event, d) {
        if (!isDragging) {
          isDragging = true;

          // Change cursor and visual feedback for drag start
          d3.select(this).style("cursor", "grabbing");
          const circle = d3.select(this).select("circle");
          circle
            .transition()
            .duration(100)
            .attr("stroke", "#fbbf24")
            .attr("stroke-width", 4)
            .attr("stroke-opacity", 1);

          // Bring to front during drag
          const parent = this.parentNode;
          if (parent) {
            parent.appendChild(this);
          }
        }

        d.fx = event.x;
        d.fy = event.y;

        // Keep bubble within bounds
        const padding = d.r + 5;
        d.fx = Math.max(padding, Math.min(width - padding, d.fx));
        d.fy = Math.max(padding, Math.min(height - padding, d.fy));
      })
      .on("end", function (event, d) {
        if (!event.active) simulation.alphaTarget(0);

        if (isDragging) {
          // Reset cursor and visual feedback only if we were actually dragging
          d3.select(this).style("cursor", "grab");
          const circle = d3.select(this).select("circle");
          circle
            .transition()
            .duration(200)
            .attr("stroke", function () {
              const baseColor = d3.color(d.color);
              return baseColor?.brighter(0.5)?.toString() || d.color;
            })
            .attr("stroke-width", 2)
            .attr("stroke-opacity", 0.7);
        }

        d.fx = null;
        d.fy = null;
        isDragging = false;
        dragStarted = false;
      });

    // Apply improved drag behavior that works with hover
    bubbles.call(dragBehavior);

    // Calculate proportional sizing based on bubble radius
    const getLogoSize = (r: number) => {
      // Scale logo size proportionally with bubble, with better min/max bounds
      const baseSize = r * 0.35;
      return Math.max(Math.min(r * 0.15, 8), Math.min(baseSize, Math.max(r * 0.6, 32)));
    };

    const getSymbolFontSize = (r: number) => {
      // Scale symbol font proportionally, ensuring readability
      const baseSize = r * 0.45;
      return Math.max(Math.min(r * 0.2, 10), Math.min(baseSize, Math.max(r * 0.8, 28)));
    };

    const getPercentageFontSize = (r: number) => {
      // Scale percentage font proportionally, slightly smaller than symbol
      const baseSize = r * 0.32;
      return Math.max(Math.min(r * 0.15, 8), Math.min(baseSize, Math.max(r * 0.6, 20)));
    };

    const getPriceFontSize = (r: number) => {
      // Scale price font proportionally, smaller than percentage
      const baseSize = r * 0.25;
      return Math.max(Math.min(r * 0.12, 6), Math.min(baseSize, Math.max(r * 0.5, 16)));
    };

    const shouldShowElement = (r: number, elementType: 'logo' | 'symbol' | 'percentage' | 'price') => {
      // Determine which elements to show based on bubble size to prevent overcrowding
      switch (elementType) {
        case 'logo':
          return r > 15; // Show logo only on medium+ bubbles
        case 'symbol':
          return true; // Always show symbol (most important)
        case 'percentage':
          return r > 20; // Show percentage on medium+ bubbles
        case 'price':
          return r > 35; // Show price only on large bubbles
        default:
          return true;
      }
    };

    const getVerticalSpacing = (r: number, logoSize: number, symbolFontSize: number, percentageFontSize: number, priceFontSize: number) => {
      // Calculate optimal vertical spacing to prevent overlap
      const logoHeight = logoSize;
      const symbolHeight = symbolFontSize;
      const percentageHeight = percentageFontSize;
      const priceHeight = priceFontSize;
      const spacing = Math.max(r * 0.08, 2); // Minimum 2px spacing

      return {
        logoY: -(logoHeight / 2 + symbolHeight / 2 + spacing),
        symbolY: 0,
        percentageY: symbolHeight / 2 + spacing + percentageHeight * 0.3,
        priceY: symbolHeight / 2 + spacing + percentageHeight * 0.3 + percentageHeight + spacing + priceHeight * 0.3
      };
    };

    // Add crypto logos with proportional sizing and optimal positioning
    bubbles
      .filter((d) => !!d.logo && shouldShowElement(d.r, 'logo')) // Only add logos if they exist and bubble is large enough
      .append("image")
      .attr("href", (d) => d.logo!)
      .attr("x", (d) => {
        const logoSize = getLogoSize(d.r);
        return -logoSize / 2;
      })
      .attr("y", (d) => {
        const logoSize = getLogoSize(d.r);
        const symbolFontSize = getSymbolFontSize(d.r);
        const percentageFontSize = getPercentageFontSize(d.r);
        const priceFontSize = getPriceFontSize(d.r);
        const spacing = getVerticalSpacing(d.r, logoSize, symbolFontSize, percentageFontSize, priceFontSize);
        return spacing.logoY;
      })
      .attr("width", (d) => getLogoSize(d.r))
      .attr("height", (d) => getLogoSize(d.r))
      .attr("pointer-events", "none")
      .style("opacity", 0.9)
      .on("error", function() {
        // Hide broken images
        d3.select(this).style("display", "none");
      });

    // Add symbol text with proportional sizing and optimal positioning
    bubbles
      .append("text")
      .attr("text-anchor", "middle")
      .attr("y", (d) => {
        const logoSize = getLogoSize(d.r);
        const symbolFontSize = getSymbolFontSize(d.r);
        const percentageFontSize = getPercentageFontSize(d.r);
        const priceFontSize = getPriceFontSize(d.r);
        const spacing = getVerticalSpacing(d.r, logoSize, symbolFontSize, percentageFontSize, priceFontSize);
        return d.logo ? spacing.symbolY : 0;
      })
      .attr("font-size", (d) => getSymbolFontSize(d.r))
      .attr("font-weight", "bold")
      .attr("fill", "#ffffff")
      .attr("pointer-events", "none")
      .style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)")
      .style("font-family", "system-ui, -apple-system, sans-serif")
      .style("dominant-baseline", "central")
      .text((d) => d.symbol);

    // Add percentage text with proportional sizing and optimal positioning
    bubbles
      .filter((d) => shouldShowElement(d.r, 'percentage')) // Only show percentage on medium+ bubbles
      .append("text")
      .attr("text-anchor", "middle")
      .attr("y", (d) => {
        const logoSize = getLogoSize(d.r);
        const symbolFontSize = getSymbolFontSize(d.r);
        const percentageFontSize = getPercentageFontSize(d.r);
        const priceFontSize = getPriceFontSize(d.r);
        const spacing = getVerticalSpacing(d.r, logoSize, symbolFontSize, percentageFontSize, priceFontSize);
        return (d.logo && shouldShowElement(d.r, 'logo')) ? spacing.percentageY : symbolFontSize * 0.8;
      })
      .attr("font-size", (d) => getPercentageFontSize(d.r))
      .attr("font-weight", "600")
      .attr("fill", "#ffffff")
      .attr("pointer-events", "none")
      .style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)")
      .style("font-family", "system-ui, -apple-system, sans-serif")
      .style("dominant-baseline", "central")
      .text((d) => `${d.change24h >= 0 ? "+" : ""}${d.change24h.toFixed(2)}%`);

    // Add price text for larger bubbles only (to prevent overcrowding)
    bubbles
      .filter((d) => shouldShowElement(d.r, 'price')) // Only show price on large bubbles
      .append("text")
      .attr("text-anchor", "middle")
      .attr("y", (d) => {
        const logoSize = getLogoSize(d.r);
        const symbolFontSize = getSymbolFontSize(d.r);
        const percentageFontSize = getPercentageFontSize(d.r);
        const priceFontSize = getPriceFontSize(d.r);
        const spacing = getVerticalSpacing(d.r, logoSize, symbolFontSize, percentageFontSize, priceFontSize);
        const hasLogo = d.logo && shouldShowElement(d.r, 'logo');
        const hasPercentage = shouldShowElement(d.r, 'percentage');

        if (hasLogo) {
          return spacing.priceY;
        } else if (hasPercentage) {
          return symbolFontSize * 0.8 + percentageFontSize + 4;
        } else {
          return symbolFontSize * 0.8;
        }
      })
      .attr("font-size", (d) => getPriceFontSize(d.r))
      .attr("font-weight", "500")
      .attr("fill", "#ffffff")
      .attr("opacity", 0.9)
      .attr("pointer-events", "none")
      .style("text-shadow", "1px 1px 3px rgba(0,0,0,0.8)")
      .style("font-family", "system-ui, -apple-system, sans-serif")
      .style("dominant-baseline", "central")
      .text((d) => {
        if (d.price < 1) {
          return `$${d.price.toFixed(4)}`;
        } else if (d.price < 1000) {
          return `$${d.price.toFixed(2)}`;
        } else {
          return `$${(d.price / 1000).toFixed(1)}k`;
        }
      });

    // Set up hover events that work properly with drag behavior
    bubbles
      .on("mouseover", function (event, d) {
        // Don't show hover effects while actively dragging
        if (isDragging) return;

        const visibleStroke = d3.select(this).select(".visible-stroke");
        const innerGlow = d3.select(this).select(".inner-glow");

        // Cancel any ongoing transitions to prevent conflicts
        visibleStroke.interrupt();
        innerGlow.interrupt();

        // Enhanced hover effect with brighter stroke and increased glow
        visibleStroke
          .transition()
          .duration(200)
          .attr("stroke-width", 5)
          .attr("stroke", "#fbbf24")
          .attr("stroke-opacity", 1);

        // Enhance the inner glow on hover
        innerGlow
          .transition()
          .duration(200)
          .attr("opacity", 1.5);

        // Bring to front by moving to end of parent (proper SVG z-ordering)
        const parent = this.parentNode;
        if (parent) {
          parent.appendChild(this);
        }

        setHoveredBubble(d);
        onBubbleHover?.(d);
      })
      .on("mouseout", function (event, d) {
        // Don't reset hover effects while actively dragging
        if (isDragging) return;

        const visibleStroke = d3.select(this).select(".visible-stroke");
        const innerGlow = d3.select(this).select(".inner-glow");

        // Cancel any ongoing transitions to prevent conflicts
        visibleStroke.interrupt();
        innerGlow.interrupt();

        // Reset to original state
        visibleStroke
          .transition()
          .duration(200)
          .attr("stroke-width", 3)
          .attr("stroke", function () {
            const baseColor = d3.color(d.color);
            return baseColor?.brighter(0.5)?.toString() || d.color;
          })
          .attr("stroke-opacity", 0.9);

        // Reset inner glow
        innerGlow
          .transition()
          .duration(200)
          .attr("opacity", 1);

        setHoveredBubble(null);
        onBubbleHover?.(null);
      });

    // Run simulation for enough iterations to settle bubbles without overlap
    for (let i = 0; i < 500; i++) {
      simulation.tick();
    }

    // Update positions on simulation tick without scaling
    simulation.on("tick", () => {
      bubbles.attr("transform", (d) => {
        return `translate(${d.x},${d.y})`;
      });
    });

    // Remove zoom functionality - bubbles should fill the screen

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [data, width, height, onBubbleHover]);

  return (
    <div className="relative" data-oid="cn3_xst">
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="bg-gradient-to-br from-gray-900 to-gray-800"
        style={{ display: "block" }}
        data-oid="vyfe8jc"
      />

      {/* Enhanced Tooltip */}
      {hoveredBubble && (
        <div
          className="absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 border-white/30 z-20 min-w-72 max-w-80 backdrop-blur-md"
          style={{
            boxShadow:
              "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)",
          }}
          data-oid="km3npxp"
        >
          <div className="flex items-center gap-3 mb-3" data-oid="jc0k_nz">
            {hoveredBubble.logo ? (
              <img
                src={hoveredBubble.logo}
                alt={hoveredBubble.name}
                className="w-10 h-10 rounded-full"
                onError={(e) => {
                  // Fallback to text if image fails to load
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm ${hoveredBubble.logo ? 'hidden' : ''}`}
              style={{
                backgroundColor:
                  hoveredBubble.change24h >= 0 ? "#22c55e" : "#ef4444",
              }}
              data-oid="dejxve:"
            >
              {hoveredBubble.symbol.charAt(0)}
            </div>
            <div data-oid="1ok57-r">
              <h3 className="font-bold text-lg text-white" data-oid="wmdqdov">
                {hoveredBubble.name}
              </h3>
              <span
                className="text-white/80 text-sm font-medium"
                data-oid="evx3o28"
              >
                ({hoveredBubble.symbol}) • Rank #{hoveredBubble.rank}
              </span>
            </div>
          </div>

          <div className="space-y-2 text-sm" data-oid="h0nfuhh">
            <div
              className="flex justify-between items-center"
              data-oid="d6yjrk9"
            >
              <span className="text-white/90 font-medium" data-oid="6m.58cj">
                Price:
              </span>
              <span className="font-bold text-lg text-white" data-oid="0bj4iet">
                $
                {hoveredBubble.price < 1
                  ? hoveredBubble.price.toFixed(4)
                  : hoveredBubble.price.toLocaleString()}
              </span>
            </div>

            <div
              className="flex justify-between items-center"
              data-oid="07x2:ab"
            >
              <span className="text-white/90 font-medium" data-oid="ot1y:3s">
                Market Cap:
              </span>
              <span className="font-semibold text-white" data-oid="t5x.htn">
                {hoveredBubble.marketCap >= 1e9
                  ? `$${(hoveredBubble.marketCap / 1e9).toFixed(2)}B`
                  : `$${(hoveredBubble.marketCap / 1e6).toFixed(2)}M`}
              </span>
            </div>

            <div
              className="flex justify-between items-center"
              data-oid="hpxbbcg"
            >
              <span className="text-white/90 font-medium" data-oid="4myuzet">
                24h Volume:
              </span>
              <span className="font-semibold text-white" data-oid="pbh7rf.">
                {hoveredBubble.volume24h >= 1e9
                  ? `$${(hoveredBubble.volume24h / 1e9).toFixed(2)}B`
                  : `$${(hoveredBubble.volume24h / 1e6).toFixed(2)}M`}
              </span>
            </div>

            <div
              className="flex justify-between items-center"
              data-oid="rli2gdt"
            >
              <span className="text-white/90 font-medium" data-oid="_87x9tp">
                24h Change:
              </span>
              <div
                className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${
                  hoveredBubble.change24h >= 0
                    ? "bg-green-500/20 text-green-300 border border-green-400/30"
                    : "bg-red-500/20 text-red-300 border border-red-400/30"
                }`}
                data-oid="wxq.s6:"
              >
                <span data-oid="rdhy:ha">
                  {hoveredBubble.change24h >= 0 ? "↗" : "↘"}
                </span>
                <span data-oid="f-r8.4q">
                  {hoveredBubble.change24h >= 0 ? "+" : ""}
                  {hoveredBubble.change24h.toFixed(2)}%
                </span>
              </div>
            </div>

            <div
              className="flex justify-between items-center"
              data-oid="a4w:gre"
            >
              <span className="text-white/90 font-medium" data-oid="n46j1tl">
                7d Change:
              </span>
              <span
                className={`font-semibold ${hoveredBubble.change7d >= 0 ? "text-green-300" : "text-red-300"}`}
                data-oid="8cu:u_m"
              >
                {hoveredBubble.change7d >= 0 ? "+" : ""}
                {hoveredBubble.change7d.toFixed(2)}%
              </span>
            </div>

            <div className="pt-2 border-t border-white/30" data-oid="_ul03a5">
              <div
                className="flex justify-between items-center"
                data-oid="yrv.:ln"
              >
                <span className="text-white/90 font-medium" data-oid="mwc:cg_">
                  Category:
                </span>
                <span
                  className="px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold"
                  data-oid="y9j.jp4"
                >
                  {hoveredBubble.category}
                </span>
              </div>
            </div>
          </div>

          <div
            className="mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium"
            data-oid="ofbq3_c"
          >
            Drag to move bubble around
          </div>
        </div>
      )}
    </div>
  );
};
