{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/BubbleChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef, useState } from \"react\";\nimport * as d3 from \"d3\";\nimport { CryptoCurrency, BubbleData } from \"@/types\";\n\ninterface BubbleChartProps {\n  data: CryptoCurrency[];\n  width?: number;\n  height?: number;\n  onBubbleClick?: (crypto: CryptoCurrency) => void;\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n}\n\nexport const BubbleChart: React.FC<BubbleChartProps> = ({\n  data,\n  width = 1200,\n  height = 800,\n  onBubbleClick,\n  onBubbleHover,\n}) => {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(\n    null,\n  );\n\n  useEffect(() => {\n    if (!svgRef.current || !data.length) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll(\"*\").remove();\n\n    // Create scales\n    const maxMarketCap = d3.max(data, (d) => d.marketCap) || 1;\n    const minMarketCap = d3.min(data, (d) => d.marketCap) || 1;\n\n    // Calculate optimal bubble sizes to fill screen without overlapping\n    const totalArea = width * height;\n    const bubbleCount = data.length;\n    const averageArea = (totalArea / bubbleCount) * 0.5; // Use 50% of available space for non-overlapping\n    const averageRadius = Math.sqrt(averageArea / Math.PI);\n\n    // Radius scale - conservative sizing to prevent overlap\n    const radiusScale = d3\n      .scaleSqrt()\n      .domain([minMarketCap, maxMarketCap])\n      .range([averageRadius * 0.4, averageRadius * 1.8]); // More conservative sizing\n\n    // Color scale for price changes with more vibrant colors\n    const colorScale = d3\n      .scaleLinear<string>()\n      .domain([-15, -5, 0, 5, 15])\n      .range([\"#dc2626\", \"#f87171\", \"#6b7280\", \"#34d399\", \"#059669\"])\n      .clamp(true);\n\n    // Prepare bubble data\n    const bubbleData: BubbleData[] = data.map((crypto) => ({\n      ...crypto,\n      x: 0,\n      y: 0,\n      r: radiusScale(crypto.marketCap),\n      color: colorScale(crypto.change24h),\n    }));\n\n    // Create force simulation for non-overlapping bubbles like CryptoBubbles\n    const simulation = d3\n      .forceSimulation<BubbleData>(bubbleData)\n      .force(\"charge\", d3.forceManyBody().strength(-50)) // Moderate repulsion to prevent overlap\n      .force(\"center\", d3.forceCenter(width / 2, height / 2))\n      .force(\n        \"collision\",\n        d3\n          .forceCollide<BubbleData>()\n          .radius((d) => d.r + 3) // Small padding to prevent overlap\n          .strength(1) // Strong collision detection\n          .iterations(3),\n      ) // Multiple iterations for better collision resolution\n      .force(\"x\", d3.forceX<BubbleData>(width / 2).strength(0.05)) // Gentle centering\n      .force(\"y\", d3.forceY<BubbleData>(height / 2).strength(0.05))\n      // Add boundary forces to keep bubbles on screen\n      .force(\"boundary\", () => {\n        bubbleData.forEach((d) => {\n          const padding = d.r + 5;\n          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));\n          d.y = Math.max(\n            padding,\n            Math.min(height - padding, d.y || height / 2),\n          );\n        });\n      })\n      .alphaDecay(0.005) // Very slow decay for thorough settling\n      .velocityDecay(0.6); // Higher friction for stability\n\n    // Create container group\n    const container = svg.append(\"g\");\n\n    // Create bubbles with entrance animation\n    const bubbles = container\n      .selectAll(\".bubble\")\n      .data(bubbleData)\n      .enter()\n      .append(\"g\")\n      .attr(\"class\", \"bubble\")\n      .style(\"cursor\", \"pointer\")\n      .style(\"opacity\", 0);\n\n    // Add circles with gradient effects and inner glow\n    const defs = svg.append(\"defs\");\n\n    // Create striking inner glow filter using bubble's own color\n    const innerGlowFilter = defs\n      .append(\"filter\")\n      .attr(\"id\", \"inner-glow\")\n      .attr(\"x\", \"-50%\")\n      .attr(\"y\", \"-50%\")\n      .attr(\"width\", \"200%\")\n      .attr(\"height\", \"200%\");\n\n    // Step 1: Create inverted alpha mask\n    innerGlowFilter\n      .append(\"feComposite\")\n      .attr(\"in\", \"SourceAlpha\")\n      .attr(\"in2\", \"SourceAlpha\")\n      .attr(\"operator\", \"out\")\n      .attr(\"result\", \"inverse\");\n\n    // Step 2: Blur the inverted mask for glow effect\n    innerGlowFilter\n      .append(\"feGaussianBlur\")\n      .attr(\"in\", \"inverse\")\n      .attr(\"stdDeviation\", \"5\")\n      .attr(\"result\", \"blurred\");\n\n    // Step 3: Clip the blur to the original shape for inner glow\n    innerGlowFilter\n      .append(\"feComposite\")\n      .attr(\"in\", \"blurred\")\n      .attr(\"in2\", \"SourceAlpha\")\n      .attr(\"operator\", \"in\")\n      .attr(\"result\", \"innerGlow\");\n\n    // Step 4: Combine with original for striking effect\n    const feMerge = innerGlowFilter.append(\"feMerge\");\n    feMerge.append(\"feMergeNode\").attr(\"in\", \"SourceGraphic\");\n    feMerge.append(\"feMergeNode\").attr(\"in\", \"innerGlow\");\n\n    // Create drop shadow filter\n    const shadowFilter = defs\n      .append(\"filter\")\n      .attr(\"id\", \"drop-shadow\")\n      .attr(\"x\", \"-50%\")\n      .attr(\"y\", \"-50%\")\n      .attr(\"width\", \"200%\")\n      .attr(\"height\", \"200%\");\n\n    shadowFilter\n      .append(\"feDropShadow\")\n      .attr(\"dx\", \"2\")\n      .attr(\"dy\", \"2\")\n      .attr(\"stdDeviation\", \"4\")\n      .attr(\"flood-color\", \"rgba(0,0,0,0.4)\");\n\n    // Create glass-effect gradients matching the reference image\n    bubbles.each(function (d, i) {\n      const gradient = defs\n        .append(\"radialGradient\")\n        .attr(\"id\", `gradient-${i}`)\n        .attr(\"cx\", \"30%\")\n        .attr(\"cy\", \"30%\");\n\n      // Create solid, striking bubble effect\n      const baseColor = d3.color(d.color);\n\n      // Bright center using the bubble's own color\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"0%\")\n        .attr(\"stop-color\", baseColor?.brighter(1.2)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.6\");\n\n      // Transition to main color\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"40%\")\n        .attr(\"stop-color\", baseColor?.brighter(0.5)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.5\");\n\n      // Main color area - more solid\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"70%\")\n        .attr(\"stop-color\", d.color)\n        .attr(\"stop-opacity\", \"0.4\");\n\n      // Dark outer edge for strong definition\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"100%\")\n        .attr(\"stop-color\", baseColor?.darker(0.5)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.8\");\n    });\n\n    // Main transparent bubble with inner glow\n    bubbles\n      .append(\"circle\")\n      .attr(\"r\", 0)\n      .attr(\"fill\", (d, i) => `url(#gradient-${i})`)\n      .attr(\"stroke\", (d) => {\n        const baseColor = d3.color(d.color);\n        return baseColor?.brighter(0.5)?.toString() || d.color;\n      })\n      .attr(\"stroke-width\", 2.5)\n      .attr(\"stroke-opacity\", 0.7)\n      .attr(\"opacity\", 0.9)\n      .attr(\"filter\", \"url(#inner-glow)\")\n      .transition()\n      .duration(1000)\n      .delay((d, i) => i * 50)\n      .attr(\"r\", (d) => d.r)\n      .on(\"end\", function () {\n        if (this.parentNode) {\n          d3.select(this.parentNode as Element).style(\"opacity\", 1);\n        }\n      });\n\n    // Enhanced hover effects with proper transform handling\n    bubbles\n      .on(\"mouseover\", function (event, d) {\n        const circle = d3.select(this).select(\"circle\");\n\n        // Cancel any ongoing transitions to prevent conflicts\n        circle.interrupt();\n\n        // Store the current scale for this bubble\n        d.hoverScale = 1.1;\n\n        // Enhance transparency and glow on hover\n        circle\n          .transition()\n          .duration(200)\n          .attr(\"opacity\", 1)\n          .attr(\"stroke-width\", 3)\n          .attr(\"stroke\", \"#fbbf24\")\n          .attr(\"stroke-opacity\", 0.8);\n\n        // Bring to front by moving to end of parent (proper SVG z-ordering)\n        const parent = this.parentNode;\n        if (parent) {\n          parent.appendChild(this);\n        }\n\n        setHoveredBubble(d);\n        onBubbleHover?.(d);\n      })\n      .on(\"mouseout\", function (event, d) {\n        const circle = d3.select(this).select(\"circle\");\n\n        // Cancel any ongoing transitions to prevent conflicts\n        circle.interrupt();\n\n        // Reset the scale for this bubble\n        d.hoverScale = 1;\n\n        // Reset to transparent state\n        circle\n          .transition()\n          .duration(200)\n          .attr(\"opacity\", 0.9)\n          .attr(\"stroke-width\", 2)\n          .attr(\"stroke\", function () {\n            const baseColor = d3.color(d.color);\n            return baseColor?.brighter(0.5)?.toString() || d.color;\n          })\n          .attr(\"stroke-opacity\", 0.7);\n\n        setHoveredBubble(null);\n        onBubbleHover?.(null);\n      })\n      // Click functionality removed - details now show on hover\n\n    // Add symbol text - show for all bubbles with adaptive sizing\n    bubbles\n      .append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", \"-0.1em\")\n      .attr(\"font-size\", (d) => Math.max(8, Math.min(d.r * 0.4, 20)))\n      .attr(\"font-weight\", \"bold\")\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"pointer-events\", \"none\")\n      .style(\"text-shadow\", \"2px 2px 4px rgba(0,0,0,0.9)\")\n      .style(\"font-family\", \"system-ui, -apple-system, sans-serif\")\n      .style(\"dominant-baseline\", \"central\")\n      .text((d) => d.symbol);\n\n    // Add percentage text - show for all bubbles\n    bubbles\n      .append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", \"0.8em\")\n      .attr(\"font-size\", (d) => Math.max(6, Math.min(d.r * 0.3, 16)))\n      .attr(\"font-weight\", \"600\")\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"pointer-events\", \"none\")\n      .style(\"text-shadow\", \"2px 2px 4px rgba(0,0,0,0.9)\")\n      .style(\"font-family\", \"system-ui, -apple-system, sans-serif\")\n      .style(\"dominant-baseline\", \"central\")\n      .text((d) => `${d.change24h >= 0 ? \"+\" : \"\"}${d.change24h.toFixed(2)}%`);\n\n    // Run simulation for enough iterations to settle bubbles without overlap\n    for (let i = 0; i < 500; i++) {\n      simulation.tick();\n    }\n\n    // Update positions on simulation tick with proper scaling\n    simulation.on(\"tick\", () => {\n      bubbles.attr(\"transform\", (d) => {\n        const scale = d.clickScale || d.hoverScale || 1;\n        return `translate(${d.x},${d.y}) scale(${scale})`;\n      });\n    });\n\n    // Remove zoom functionality - bubbles should fill the screen\n\n    // Cleanup\n    return () => {\n      simulation.stop();\n    };\n  }, [data, width, height, onBubbleHover]);\n\n  return (\n    <div className=\"relative\" data-oid=\"cn3_xst\">\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        className=\"bg-gradient-to-br from-gray-900 to-gray-800\"\n        style={{ display: \"block\" }}\n        data-oid=\"vyfe8jc\"\n      />\n\n      {/* Enhanced Tooltip */}\n      {hoveredBubble && (\n        <div\n          className=\"absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 border-white/30 z-20 min-w-72 max-w-80 backdrop-blur-md\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"km3npxp\"\n        >\n          <div className=\"flex items-center gap-3 mb-3\" data-oid=\"jc0k_nz\">\n            <div\n              className=\"w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm\"\n              style={{\n                backgroundColor:\n                  hoveredBubble.change24h >= 0 ? \"#22c55e\" : \"#ef4444\",\n              }}\n              data-oid=\"dejxve:\"\n            >\n              {hoveredBubble.symbol.charAt(0)}\n            </div>\n            <div data-oid=\"1ok57-r\">\n              <h3 className=\"font-bold text-lg text-white\" data-oid=\"wmdqdov\">\n                {hoveredBubble.name}\n              </h3>\n              <span\n                className=\"text-white/80 text-sm font-medium\"\n                data-oid=\"evx3o28\"\n              >\n                ({hoveredBubble.symbol}) • Rank #{hoveredBubble.rank}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"space-y-2 text-sm\" data-oid=\"h0nfuhh\">\n            <div\n              className=\"flex justify-between items-center\"\n              data-oid=\"d6yjrk9\"\n            >\n              <span className=\"text-white/90 font-medium\" data-oid=\"6m.58cj\">\n                Price:\n              </span>\n              <span className=\"font-bold text-lg text-white\" data-oid=\"0bj4iet\">\n                $\n                {hoveredBubble.price < 1\n                  ? hoveredBubble.price.toFixed(4)\n                  : hoveredBubble.price.toLocaleString()}\n              </span>\n            </div>\n\n            <div\n              className=\"flex justify-between items-center\"\n              data-oid=\"07x2:ab\"\n            >\n              <span className=\"text-white/90 font-medium\" data-oid=\"ot1y:3s\">\n                Market Cap:\n              </span>\n              <span className=\"font-semibold text-white\" data-oid=\"t5x.htn\">\n                {hoveredBubble.marketCap >= 1e9\n                  ? `$${(hoveredBubble.marketCap / 1e9).toFixed(2)}B`\n                  : `$${(hoveredBubble.marketCap / 1e6).toFixed(2)}M`}\n              </span>\n            </div>\n\n            <div\n              className=\"flex justify-between items-center\"\n              data-oid=\"hpxbbcg\"\n            >\n              <span className=\"text-white/90 font-medium\" data-oid=\"4myuzet\">\n                24h Volume:\n              </span>\n              <span className=\"font-semibold text-white\" data-oid=\"pbh7rf.\">\n                {hoveredBubble.volume24h >= 1e9\n                  ? `$${(hoveredBubble.volume24h / 1e9).toFixed(2)}B`\n                  : `$${(hoveredBubble.volume24h / 1e6).toFixed(2)}M`}\n              </span>\n            </div>\n\n            <div\n              className=\"flex justify-between items-center\"\n              data-oid=\"rli2gdt\"\n            >\n              <span className=\"text-white/90 font-medium\" data-oid=\"_87x9tp\">\n                24h Change:\n              </span>\n              <div\n                className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${\n                  hoveredBubble.change24h >= 0\n                    ? \"bg-green-500/20 text-green-300 border border-green-400/30\"\n                    : \"bg-red-500/20 text-red-300 border border-red-400/30\"\n                }`}\n                data-oid=\"wxq.s6:\"\n              >\n                <span data-oid=\"rdhy:ha\">\n                  {hoveredBubble.change24h >= 0 ? \"↗\" : \"↘\"}\n                </span>\n                <span data-oid=\"f-r8.4q\">\n                  {hoveredBubble.change24h >= 0 ? \"+\" : \"\"}\n                  {hoveredBubble.change24h.toFixed(2)}%\n                </span>\n              </div>\n            </div>\n\n            <div\n              className=\"flex justify-between items-center\"\n              data-oid=\"a4w:gre\"\n            >\n              <span className=\"text-white/90 font-medium\" data-oid=\"n46j1tl\">\n                7d Change:\n              </span>\n              <span\n                className={`font-semibold ${hoveredBubble.change7d >= 0 ? \"text-green-300\" : \"text-red-300\"}`}\n                data-oid=\"8cu:u_m\"\n              >\n                {hoveredBubble.change7d >= 0 ? \"+\" : \"\"}\n                {hoveredBubble.change7d.toFixed(2)}%\n              </span>\n            </div>\n\n            <div className=\"pt-2 border-t border-white/30\" data-oid=\"_ul03a5\">\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"yrv.:ln\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"mwc:cg_\">\n                  Category:\n                </span>\n                <span\n                  className=\"px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold\"\n                  data-oid=\"y9j.jp4\"\n                >\n                  {hoveredBubble.category}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div\n            className=\"mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium\"\n            data-oid=\"ofbq3_c\"\n          >\n            Drag to move bubble around\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAcO,MAAM,cAA0C,CAAC,EACtD,IAAI,EACJ,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,EACb,aAAa,EACd;;IACC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C;IAGF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE;YAErC,MAAM,MAAM,CAAA,GAAA,wLAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO;YACpC,IAAI,SAAS,CAAC,KAAK,MAAM;YAEzB,gBAAgB;YAChB,MAAM,eAAe,CAAA,GAAA,8KAAA,CAAA,MAAM,AAAD,EAAE;yCAAM,CAAC,IAAM,EAAE,SAAS;2CAAK;YACzD,MAAM,eAAe,CAAA,GAAA,8KAAA,CAAA,MAAM,AAAD,EAAE;yCAAM,CAAC,IAAM,EAAE,SAAS;2CAAK;YAEzD,oEAAoE;YACpE,MAAM,YAAY,QAAQ;YAC1B,MAAM,cAAc,KAAK,MAAM;YAC/B,MAAM,cAAc,AAAC,YAAY,cAAe,KAAK,iDAAiD;YACtG,MAAM,gBAAgB,KAAK,IAAI,CAAC,cAAc,KAAK,EAAE;YAErD,wDAAwD;YACxD,MAAM,cAAc,CAAA,GAAA,iLAAA,CAAA,YACR,AAAD,IACR,MAAM,CAAC;gBAAC;gBAAc;aAAa,EACnC,KAAK,CAAC;gBAAC,gBAAgB;gBAAK,gBAAgB;aAAI,GAAG,2BAA2B;YAEjF,yDAAyD;YACzD,MAAM,aAAa,CAAA,GAAA,yLAAA,CAAA,cACL,AAAD,IACV,MAAM,CAAC;gBAAC,CAAC;gBAAI,CAAC;gBAAG;gBAAG;gBAAG;aAAG,EAC1B,KAAK,CAAC;gBAAC;gBAAW;gBAAW;gBAAW;gBAAW;aAAU,EAC7D,KAAK,CAAC;YAET,sBAAsB;YACtB,MAAM,aAA2B,KAAK,GAAG;oDAAC,CAAC,SAAW,CAAC;wBACrD,GAAG,MAAM;wBACT,GAAG;wBACH,GAAG;wBACH,GAAG,YAAY,OAAO,SAAS;wBAC/B,OAAO,WAAW,OAAO,SAAS;oBACpC,CAAC;;YAED,yEAAyE;YACzE,MAAM,aAAa,CAAA,GAAA,iMAAA,CAAA,kBACD,AAAD,EAAc,YAC5B,KAAK,CAAC,UAAU,CAAA,GAAA,6LAAA,CAAA,gBAAgB,AAAD,IAAI,QAAQ,CAAC,CAAC,KAAK,wCAAwC;aAC1F,KAAK,CAAC,UAAU,CAAA,GAAA,yLAAA,CAAA,cAAc,AAAD,EAAE,QAAQ,GAAG,SAAS,IACnD,KAAK,CACJ,aACA,CAAA,GAAA,2LAAA,CAAA,eACe,AAAD,IACX,MAAM;oDAAC,CAAC,IAAM,EAAE,CAAC,GAAG;mDAAG,mCAAmC;aAC1D,QAAQ,CAAC,GAAG,6BAA6B;aACzC,UAAU,CAAC,IACd,sDAAsD;aACvD,KAAK,CAAC,KAAK,CAAA,GAAA,+KAAA,CAAA,SAAS,AAAD,EAAc,QAAQ,GAAG,QAAQ,CAAC,OAAO,mBAAmB;aAC/E,KAAK,CAAC,KAAK,CAAA,GAAA,+KAAA,CAAA,SAAS,AAAD,EAAc,SAAS,GAAG,QAAQ,CAAC,MACvD,gDAAgD;aAC/C,KAAK,CAAC;oDAAY;oBACjB,WAAW,OAAO;4DAAC,CAAC;4BAClB,MAAM,UAAU,EAAE,CAAC,GAAG;4BACtB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,CAAC,IAAI,QAAQ;4BACjE,EAAE,CAAC,GAAG,KAAK,GAAG,CACZ,SACA,KAAK,GAAG,CAAC,SAAS,SAAS,EAAE,CAAC,IAAI,SAAS;wBAE/C;;gBACF;mDACC,UAAU,CAAC,OAAO,wCAAwC;aAC1D,aAAa,CAAC,MAAM,gCAAgC;YAEvD,yBAAyB;YACzB,MAAM,YAAY,IAAI,MAAM,CAAC;YAE7B,yCAAyC;YACzC,MAAM,UAAU,UACb,SAAS,CAAC,WACV,IAAI,CAAC,YACL,KAAK,GACL,MAAM,CAAC,KACP,IAAI,CAAC,SAAS,UACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,WAAW;YAEpB,mDAAmD;YACnD,MAAM,OAAO,IAAI,MAAM,CAAC;YAExB,6DAA6D;YAC7D,MAAM,kBAAkB,KACrB,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,cACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;YAElB,qCAAqC;YACrC,gBACG,MAAM,CAAC,eACP,IAAI,CAAC,MAAM,eACX,IAAI,CAAC,OAAO,eACZ,IAAI,CAAC,YAAY,OACjB,IAAI,CAAC,UAAU;YAElB,iDAAiD;YACjD,gBACG,MAAM,CAAC,kBACP,IAAI,CAAC,MAAM,WACX,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,UAAU;YAElB,6DAA6D;YAC7D,gBACG,MAAM,CAAC,eACP,IAAI,CAAC,MAAM,WACX,IAAI,CAAC,OAAO,eACZ,IAAI,CAAC,YAAY,MACjB,IAAI,CAAC,UAAU;YAElB,oDAAoD;YACpD,MAAM,UAAU,gBAAgB,MAAM,CAAC;YACvC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;YACzC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;YAEzC,4BAA4B;YAC5B,MAAM,eAAe,KAClB,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,eACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;YAElB,aACG,MAAM,CAAC,gBACP,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,eAAe;YAEvB,6DAA6D;YAC7D,QAAQ,IAAI;yCAAC,SAAU,CAAC,EAAE,CAAC;oBACzB,MAAM,WAAW,KACd,MAAM,CAAC,kBACP,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,EAC1B,IAAI,CAAC,MAAM,OACX,IAAI,CAAC,MAAM;oBAEd,uCAAuC;oBACvC,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;oBAElC,6CAA6C;oBAC7C,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,MACf,IAAI,CAAC,cAAc,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK,EAClE,IAAI,CAAC,gBAAgB;oBAExB,2BAA2B;oBAC3B,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,OACf,IAAI,CAAC,cAAc,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK,EAClE,IAAI,CAAC,gBAAgB;oBAExB,+BAA+B;oBAC/B,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,OACf,IAAI,CAAC,cAAc,EAAE,KAAK,EAC1B,IAAI,CAAC,gBAAgB;oBAExB,wCAAwC;oBACxC,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,cAAc,WAAW,OAAO,MAAM,cAAc,EAAE,KAAK,EAChE,IAAI,CAAC,gBAAgB;gBAC1B;;YAEA,0CAA0C;YAC1C,QACG,MAAM,CAAC,UACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC;yCAAQ,CAAC,GAAG,IAAM,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;wCAC5C,IAAI,CAAC;yCAAU,CAAC;oBACf,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;oBAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;gBACxD;wCACC,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,kBAAkB,KACvB,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,UAAU,oBACf,UAAU,GACV,QAAQ,CAAC,MACT,KAAK;yCAAC,CAAC,GAAG,IAAM,IAAI;wCACpB,IAAI,CAAC;yCAAK,CAAC,IAAM,EAAE,CAAC;wCACpB,EAAE,CAAC;yCAAO;oBACT,IAAI,IAAI,CAAC,UAAU,EAAE;wBACnB,CAAA,GAAA,wLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,CAAC,UAAU,EAAa,KAAK,CAAC,WAAW;oBACzD;gBACF;;YAEF,wDAAwD;YACxD,QACG,EAAE,CAAC;yCAAa,SAAU,KAAK,EAAE,CAAC;oBACjC,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;oBAEtC,sDAAsD;oBACtD,OAAO,SAAS;oBAEhB,0CAA0C;oBAC1C,EAAE,UAAU,GAAG;oBAEf,yCAAyC;oBACzC,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU,WACf,IAAI,CAAC,kBAAkB;oBAE1B,oEAAoE;oBACpE,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC9B,IAAI,QAAQ;wBACV,OAAO,WAAW,CAAC,IAAI;oBACzB;oBAEA,iBAAiB;oBACjB,gBAAgB;gBAClB;wCACC,EAAE,CAAC;yCAAY,SAAU,KAAK,EAAE,CAAC;oBAChC,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;oBAEtC,sDAAsD;oBACtD,OAAO,SAAS;oBAEhB,kCAAkC;oBAClC,EAAE,UAAU,GAAG;oBAEf,6BAA6B;oBAC7B,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC;iDAAU;4BACd,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;4BAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;wBACxD;gDACC,IAAI,CAAC,kBAAkB;oBAE1B,iBAAiB;oBACjB,gBAAgB;gBAClB;;YACA,0DAA0D;YAE5D,8DAA8D;YAC9D,QACG,MAAM,CAAC,QACP,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,UACX,IAAI,CAAC;yCAAa,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK;wCACzD,IAAI,CAAC,eAAe,QACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI;yCAAC,CAAC,IAAM,EAAE,MAAM;;YAEvB,6CAA6C;YAC7C,QACG,MAAM,CAAC,QACP,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,SACX,IAAI,CAAC;yCAAa,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK;wCACzD,IAAI,CAAC,eAAe,OACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI;yCAAC,CAAC,IAAM,GAAG,EAAE,SAAS,IAAI,IAAI,MAAM,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;YAEzE,yEAAyE;YACzE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,WAAW,IAAI;YACjB;YAEA,0DAA0D;YAC1D,WAAW,EAAE,CAAC;yCAAQ;oBACpB,QAAQ,IAAI,CAAC;iDAAa,CAAC;4BACzB,MAAM,QAAQ,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;4BAC9C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACnD;;gBACF;;YAEA,6DAA6D;YAE7D,UAAU;YACV;yCAAO;oBACL,WAAW,IAAI;gBACjB;;QACF;gCAAG;QAAC;QAAM;QAAO;QAAQ;KAAc;IAEvC,qBACE,6LAAC;QAAI,WAAU;QAAW,YAAS;;0BACjC,6LAAC;gBACC,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAQ;gBAC1B,YAAS;;;;;;YAIV,+BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WACE;gBACJ;gBACA,YAAS;;kCAET,6LAAC;wBAAI,WAAU;wBAA+B,YAAS;;0CACrD,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBACE,cAAc,SAAS,IAAI,IAAI,YAAY;gCAC/C;gCACA,YAAS;0CAER,cAAc,MAAM,CAAC,MAAM,CAAC;;;;;;0CAE/B,6LAAC;gCAAI,YAAS;;kDACZ,6LAAC;wCAAG,WAAU;wCAA+B,YAAS;kDACnD,cAAc,IAAI;;;;;;kDAErB,6LAAC;wCACC,WAAU;wCACV,YAAS;;4CACV;4CACG,cAAc,MAAM;4CAAC;4CAAW,cAAc,IAAI;;;;;;;;;;;;;;;;;;;kCAK1D,6LAAC;wBAAI,WAAU;wBAAoB,YAAS;;0CAC1C,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,WAAU;wCAA4B,YAAS;kDAAU;;;;;;kDAG/D,6LAAC;wCAAK,WAAU;wCAA+B,YAAS;;4CAAU;4CAE/D,cAAc,KAAK,GAAG,IACnB,cAAc,KAAK,CAAC,OAAO,CAAC,KAC5B,cAAc,KAAK,CAAC,cAAc;;;;;;;;;;;;;0CAI1C,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,WAAU;wCAA4B,YAAS;kDAAU;;;;;;kDAG/D,6LAAC;wCAAK,WAAU;wCAA2B,YAAS;kDACjD,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;0CAIzD,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,WAAU;wCAA4B,YAAS;kDAAU;;;;;;kDAG/D,6LAAC;wCAAK,WAAU;wCAA2B,YAAS;kDACjD,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;0CAIzD,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,WAAU;wCAA4B,YAAS;kDAAU;;;;;;kDAG/D,6LAAC;wCACC,WAAW,CAAC,iEAAiE,EAC3E,cAAc,SAAS,IAAI,IACvB,8DACA,uDACJ;wCACF,YAAS;;0DAET,6LAAC;gDAAK,YAAS;0DACZ,cAAc,SAAS,IAAI,IAAI,MAAM;;;;;;0DAExC,6LAAC;gDAAK,YAAS;;oDACZ,cAAc,SAAS,IAAI,IAAI,MAAM;oDACrC,cAAc,SAAS,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK1C,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,WAAU;wCAA4B,YAAS;kDAAU;;;;;;kDAG/D,6LAAC;wCACC,WAAW,CAAC,cAAc,EAAE,cAAc,QAAQ,IAAI,IAAI,mBAAmB,gBAAgB;wCAC7F,YAAS;;4CAER,cAAc,QAAQ,IAAI,IAAI,MAAM;4CACpC,cAAc,QAAQ,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAIvC,6LAAC;gCAAI,WAAU;gCAAgC,YAAS;0CACtD,cAAA,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAER,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC;wBACC,WAAU;wBACV,YAAS;kCACV;;;;;;;;;;;;;;;;;;AAOX;GAzda;KAAA", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/LoadingSpinner.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  text?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = \"md\",\n  text = \"Loading...\",\n}) => {\n  const sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-8 h-8\",\n    lg: \"w-12 h-12\",\n  };\n\n  return (\n    <div\n      className=\"flex flex-col items-center justify-center p-8\"\n      data-oid=\"6gmp8xi\"\n    >\n      <div\n        className={`${sizeClasses[size]} animate-spin rounded-full border-4 border-gray-300 border-t-blue-600`}\n        data-oid=\"o81jg6y\"\n      ></div>\n      {text && (\n        <p className=\"mt-4 text-gray-600 text-sm\" data-oid=\"ovy93vh\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport const BubbleChartSkeleton: React.FC = () => {\n  return (\n    <div\n      className=\"w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center\"\n      data-oid=\"n18j76x\"\n    >\n      <div className=\"text-center\" data-oid=\"q_ecqx.\">\n        <div\n          className=\"w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4\"\n          data-oid=\"4jdehoc\"\n        ></div>\n        <p className=\"text-white text-lg font-medium\" data-oid=\":f8jvvv\">\n          Loading cryptocurrency data...\n        </p>\n        <p className=\"text-gray-400 text-sm mt-2\" data-oid=\"h0lsp4m\">\n          Preparing interactive bubble chart\n        </p>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,IAAI,EACX,OAAO,YAAY,EACpB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,YAAS;;0BAET,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;gBACtG,YAAS;;;;;;YAEV,sBACC,6LAAC;gBAAE,WAAU;gBAA6B,YAAS;0BAChD;;;;;;;;;;;;AAKX;KA1Ba;AA4BN,MAAM,sBAAgC;IAC3C,qBACE,6LAAC;QACC,WAAU;QACV,YAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;YAAc,YAAS;;8BACpC,6LAAC;oBACC,WAAU;oBACV,YAAS;;;;;;8BAEX,6LAAC;oBAAE,WAAU;oBAAiC,YAAS;8BAAU;;;;;;8BAGjE,6LAAC;oBAAE,WAAU;oBAA6B,YAAS;8BAAU;;;;;;;;;;;;;;;;;AAMrE;MApBa", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/ResponsiveBubbleChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Bubble<PERSON>hart } from \"./BubbleC<PERSON>\";\nimport { BubbleChartSkeleton } from \"./LoadingSpinner\";\nimport { CryptoCurrency } from \"@/types\";\n\ninterface ResponsiveBubbleChartProps {\n  data: CryptoCurrency[];\n  onBubbleClick?: (crypto: CryptoCurrency) => void;\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n  isLoading?: boolean;\n}\n\nexport const ResponsiveBubbleChart: React.FC<ResponsiveBubbleChartProps> = ({\n  data,\n  onBubbleClick,\n  onBubbleHover,\n  isLoading = false,\n}) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [dimensions, setDimensions] = useState({\n    width: typeof window !== \"undefined\" ? window.innerWidth : 1920,\n    height: typeof window !== \"undefined\" ? window.innerHeight : 1080,\n  });\n\n  useEffect(() => {\n    const updateDimensions = () => {\n      // Use full viewport dimensions\n      setDimensions({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    updateDimensions();\n    window.addEventListener(\"resize\", updateDimensions);\n\n    return () => window.removeEventListener(\"resize\", updateDimensions);\n  }, []);\n\n  if (isLoading) {\n    return <BubbleChartSkeleton />;\n  }\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 w-full h-full\">\n      <BubbleChart\n        data={data}\n        width={dimensions.width}\n        height={dimensions.height}\n        onBubbleClick={onBubbleClick}\n        onBubbleHover={onBubbleHover}\n      />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAcO,MAAM,wBAA8D,CAAC,EAC1E,IAAI,EACJ,aAAa,EACb,aAAa,EACb,YAAY,KAAK,EAClB;;IACC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO,uCAAgC,OAAO,UAAU;QACxD,QAAQ,uCAAgC,OAAO,WAAW;IAC5D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM;oEAAmB;oBACvB,+BAA+B;oBAC/B,cAAc;wBACZ,OAAO,OAAO,UAAU;wBACxB,QAAQ,OAAO,WAAW;oBAC5B;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;mDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;0CAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBAAO,6LAAC,uIAAA,CAAA,sBAAmB;;;;;IAC7B;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAU;kBAChC,cAAA,6LAAC,oIAAA,CAAA,cAAW;YACV,MAAM;YACN,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,eAAe;YACf,eAAe;;;;;;;;;;;AAIvB;GA1Ca;KAAA", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/SearchAndFilter.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Search, Filter, TrendingUp, TrendingDown } from \"lucide-react\";\nimport { FilterOptions } from \"@/types\";\n\ninterface SearchAndFilterProps {\n  filters: FilterOptions;\n  onFiltersChange: (filters: FilterOptions) => void;\n  categories: string[];\n  compact?: boolean;\n}\n\nexport const SearchAndFilter: React.FC<SearchAndFilterProps> = ({\n  filters,\n  onFiltersChange,\n  categories,\n  compact = false,\n}) => {\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onFiltersChange({ ...filters, searchTerm: e.target.value });\n  };\n\n  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onFiltersChange({\n      ...filters,\n      category: e.target.value === \"all\" ? undefined : e.target.value,\n    });\n  };\n\n  const handleSortChange = (sortBy: FilterOptions[\"sortBy\"]) => {\n    const newSortOrder =\n      filters.sortBy === sortBy && filters.sortOrder === \"desc\"\n        ? \"asc\"\n        : \"desc\";\n    onFiltersChange({ ...filters, sortBy, sortOrder: newSortOrder });\n  };\n\n  if (compact) {\n    return (\n      <div className=\"space-y-3\">\n        {/* Compact Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-2 top-1/2 transform -translate-y-1/2 text-white/70 w-3 h-3\" />\n\n          <input\n            type=\"text\"\n            placeholder=\"Search...\"\n            value={filters.searchTerm || \"\"}\n            onChange={handleSearchChange}\n            className=\"w-full pl-7 pr-3 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 placeholder-white/60 backdrop-blur-sm\"\n          />\n        </div>\n\n        {/* Compact Category Filter */}\n        <select\n          value={filters.category || \"all\"}\n          onChange={handleCategoryChange}\n          className=\"w-full px-2 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 backdrop-blur-sm\"\n        >\n          <option value=\"all\">All Categories</option>\n          {categories.map((category) => (\n            <option key={category} value={category}>\n              {category}\n            </option>\n          ))}\n        </select>\n\n        {/* Compact Sort Options */}\n        <div className=\"flex gap-1\">\n          <button\n            onClick={() => handleSortChange(\"marketCap\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"marketCap\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            Cap\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"change24h\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"change24h\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            24h\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"price\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"price\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            Price\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg border mb-6\">\n      <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n        {/* Search */}\n        <div className=\"relative flex-1 min-w-64\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-4 h-4\" />\n\n          <input\n            type=\"text\"\n            placeholder=\"Search cryptocurrencies...\"\n            value={filters.searchTerm || \"\"}\n            onChange={handleSearchChange}\n            className=\"w-full pl-10 pr-4 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-600\"\n          />\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"flex items-center gap-2\">\n          <Filter className=\"text-gray-600 w-4 h-4\" />\n          <select\n            value={filters.category || \"all\"}\n            onChange={handleCategoryChange}\n            className=\"px-3 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Categories</option>\n            {categories.map((category) => (\n              <option key={category} value={category}>\n                {category}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Sort Options */}\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => handleSortChange(\"marketCap\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"marketCap\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            Market Cap\n            {filters.sortBy === \"marketCap\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"change24h\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"change24h\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            24h Change\n            {filters.sortBy === \"change24h\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"price\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"price\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            Price\n            {filters.sortBy === \"price\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters Display */}\n      {(filters.searchTerm || filters.category) && (\n        <div className=\"mt-4 flex flex-wrap gap-2\">\n          {filters.searchTerm && (\n            <span className=\"px-3 py-1 bg-blue-100 text-blue-900 rounded-full text-sm font-semibold\">\n              Search: &ldquo;{filters.searchTerm}&rdquo;\n              <button\n                onClick={() =>\n                  onFiltersChange({ ...filters, searchTerm: undefined })\n                }\n                className=\"ml-2 text-blue-700 hover:text-blue-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          {filters.category && (\n            <span className=\"px-3 py-1 bg-green-100 text-green-900 rounded-full text-sm font-semibold\">\n              Category: {filters.category}\n              <button\n                onClick={() =>\n                  onFiltersChange({ ...filters, category: undefined })\n                }\n                className=\"ml-2 text-green-700 hover:text-green-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAaO,MAAM,kBAAkD,CAAC,EAC9D,OAAO,EACP,eAAe,EACf,UAAU,EACV,UAAU,KAAK,EAChB;IACC,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YAAE,GAAG,OAAO;YAAE,YAAY,EAAE,MAAM,CAAC,KAAK;QAAC;IAC3D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;YACd,GAAG,OAAO;YACV,UAAU,EAAE,MAAM,CAAC,KAAK,KAAK,QAAQ,YAAY,EAAE,MAAM,CAAC,KAAK;QACjE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eACJ,QAAQ,MAAM,KAAK,UAAU,QAAQ,SAAS,KAAK,SAC/C,QACA;QACN,gBAAgB;YAAE,GAAG,OAAO;YAAE;YAAQ,WAAW;QAAa;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAElB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,QAAQ,UAAU,IAAI;4BAC7B,UAAU;4BACV,WAAU;;;;;;;;;;;;8BAKd,6LAAC;oBACC,OAAO,QAAQ,QAAQ,IAAI;oBAC3B,UAAU;oBACV,WAAU;;sCAEV,6LAAC;4BAAO,OAAM;sCAAM;;;;;;wBACnB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gCAAsB,OAAO;0CAC3B;+BADU;;;;;;;;;;;8BAOjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,UACf,2CACA,6EACJ;sCACH;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAElB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU,IAAI;gCAC7B,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,OAAO,QAAQ,QAAQ,IAAI;gCAC3B,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;oCACnB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAAsB,OAAO;sDAC3B;2CADU;;;;;;;;;;;;;;;;;kCAQnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;0CAGL,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;0CAGL,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,UACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,WAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;;;;;;;;;;;;;YAMR,CAAC,QAAQ,UAAU,IAAI,QAAQ,QAAQ,mBACtC,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,UAAU,kBACjB,6LAAC;wBAAK,WAAU;;4BAAyE;4BACvE,QAAQ,UAAU;4BAAC;0CACnC,6LAAC;gCACC,SAAS,IACP,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,YAAY;oCAAU;gCAEtD,WAAU;0CACX;;;;;;;;;;;;oBAKJ,QAAQ,QAAQ,kBACf,6LAAC;wBAAK,WAAU;;4BAA2E;4BAC9E,QAAQ,QAAQ;0CAC3B,6LAAC;gCACC,SAAS,IACP,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,UAAU;oCAAU;gCAEpD,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAvNa", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/CryptoDetailModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport {\n  X,\n  TrendingUp,\n  TrendingDown,\n  DollarSign,\n  BarChart3,\n  Volume2,\n} from \"lucide-react\";\nimport { CryptoCurrency } from \"@/types\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface CryptoDetailModalProps {\n  crypto: CryptoCurrency | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport const CryptoDetailModal: React.FC<CryptoDetailModalProps> = ({\n  crypto,\n  isOpen,\n  onClose,\n}) => {\n  if (!crypto) return null;\n\n  const formatCurrency = (value: number) => {\n    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;\n    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;\n    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;\n    return `$${value.toFixed(2)}`;\n  };\n\n  const formatNumber = (value: number) => {\n    return value.toLocaleString();\n  };\n\n  return (\n    <AnimatePresence data-oid=\"dxsnpvm\">\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-50 flex items-center justify-center\"\n          data-oid=\"kjx7j-x\"\n        >\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black bg-opacity-50\"\n            onClick={onClose}\n            data-oid=\"ft-nn_v\"\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            className=\"relative bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\"\n            data-oid=\"br73dzc\"\n          >\n            {/* Header */}\n            <div\n              className=\"flex items-center justify-between p-6 border-b\"\n              data-oid=\"42qxy-c\"\n            >\n              <div className=\"flex items-center gap-4\" data-oid=\"lze03yr\">\n                <div\n                  className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg\"\n                  data-oid=\"wu_vcr6\"\n                >\n                  {crypto.symbol.charAt(0)}\n                </div>\n                <div data-oid=\"ldlglq-\">\n                  <h2\n                    className=\"text-2xl font-bold text-gray-900\"\n                    data-oid=\"g1yb3h2\"\n                  >\n                    {crypto.name}\n                  </h2>\n                  <p className=\"text-gray-500\" data-oid=\".jp34zt\">\n                    {crypto.symbol} • Rank #{crypto.rank}\n                  </p>\n                </div>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n                data-oid=\".wc29:q\"\n              >\n                <X className=\"w-6 h-6 text-gray-500\" data-oid=\":gy8:3i\" />\n              </button>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6\" data-oid=\"swk:tev\">\n              {/* Price Section */}\n              <div className=\"mb-8\" data-oid=\"x90c37r\">\n                <div\n                  className=\"flex items-center gap-4 mb-4\"\n                  data-oid=\"pq_xwji\"\n                >\n                  <span\n                    className=\"text-4xl font-bold text-gray-900\"\n                    data-oid=\"x._kmty\"\n                  >\n                    ${formatNumber(crypto.price)}\n                  </span>\n                  <div\n                    className={`flex items-center gap-1 px-3 py-1 rounded-full ${\n                      crypto.change24h >= 0\n                        ? \"bg-green-100 text-green-800\"\n                        : \"bg-red-100 text-red-800\"\n                    }`}\n                    data-oid=\"bb7cml1\"\n                  >\n                    {crypto.change24h >= 0 ? (\n                      <TrendingUp className=\"w-4 h-4\" data-oid=\"7ab2wly\" />\n                    ) : (\n                      <TrendingDown className=\"w-4 h-4\" data-oid=\"s_ui9t-\" />\n                    )}\n                    <span className=\"font-semibold\" data-oid=\"8u5zds6\">\n                      {crypto.change24h >= 0 ? \"+\" : \"\"}\n                      {crypto.change24h.toFixed(2)}%\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"text-sm text-gray-600\" data-oid=\"ie.1brh\">\n                  7d change:\n                  <span\n                    className={`ml-1 font-semibold ${\n                      crypto.change7d >= 0 ? \"text-green-600\" : \"text-red-600\"\n                    }`}\n                    data-oid=\"6mve8sp\"\n                  >\n                    {crypto.change7d >= 0 ? \"+\" : \"\"}\n                    {crypto.change7d.toFixed(2)}%\n                  </span>\n                </div>\n              </div>\n\n              {/* Stats Grid */}\n              <div\n                className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\"\n                data-oid=\"i:i:mv1\"\n              >\n                <div className=\"bg-gray-50 p-4 rounded-lg\" data-oid=\"-gov4su\">\n                  <div\n                    className=\"flex items-center gap-2 mb-2\"\n                    data-oid=\"r2:c.zl\"\n                  >\n                    <BarChart3\n                      className=\"w-5 h-5 text-blue-500\"\n                      data-oid=\"gtwkr.o\"\n                    />\n\n                    <span\n                      className=\"font-semibold text-gray-700\"\n                      data-oid=\"ine-s5n\"\n                    >\n                      Market Cap\n                    </span>\n                  </div>\n                  <p\n                    className=\"text-2xl font-bold text-gray-900\"\n                    data-oid=\"8kpvxmu\"\n                  >\n                    {formatCurrency(crypto.marketCap)}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\" data-oid=\"ivibyej\">\n                  <div\n                    className=\"flex items-center gap-2 mb-2\"\n                    data-oid=\".2dh-_-\"\n                  >\n                    <Volume2\n                      className=\"w-5 h-5 text-purple-500\"\n                      data-oid=\"9_a1pib\"\n                    />\n\n                    <span\n                      className=\"font-semibold text-gray-700\"\n                      data-oid=\"0_:03pj\"\n                    >\n                      24h Volume\n                    </span>\n                  </div>\n                  <p\n                    className=\"text-2xl font-bold text-gray-900\"\n                    data-oid=\"0tanxyf\"\n                  >\n                    {formatCurrency(crypto.volume24h)}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\" data-oid=\"wqt9sua\">\n                  <div\n                    className=\"flex items-center gap-2 mb-2\"\n                    data-oid=\"i2bg_gm\"\n                  >\n                    <DollarSign\n                      className=\"w-5 h-5 text-green-500\"\n                      data-oid=\"356mp4t\"\n                    />\n\n                    <span\n                      className=\"font-semibold text-gray-700\"\n                      data-oid=\"4tejedu\"\n                    >\n                      Category\n                    </span>\n                  </div>\n                  <p\n                    className=\"text-xl font-bold text-gray-900\"\n                    data-oid=\"dfdsxfe\"\n                  >\n                    {crypto.category}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\" data-oid=\"827hbsi\">\n                  <div\n                    className=\"flex items-center gap-2 mb-2\"\n                    data-oid=\"u670c94\"\n                  >\n                    <TrendingUp\n                      className=\"w-5 h-5 text-orange-500\"\n                      data-oid=\"hr1-swv\"\n                    />\n\n                    <span\n                      className=\"font-semibold text-gray-700\"\n                      data-oid=\"rxlujaw\"\n                    >\n                      Volume/MCap\n                    </span>\n                  </div>\n                  <p\n                    className=\"text-xl font-bold text-gray-900\"\n                    data-oid=\"58e7p21\"\n                  >\n                    {((crypto.volume24h / crypto.marketCap) * 100).toFixed(2)}%\n                  </p>\n                </div>\n              </div>\n\n              {/* Description */}\n              {crypto.description && (\n                <div className=\"mb-6\" data-oid=\"4lh4opq\">\n                  <h3\n                    className=\"text-lg font-semibold text-gray-900 mb-2\"\n                    data-oid=\"g-f88hd\"\n                  >\n                    About\n                  </h3>\n                  <p\n                    className=\"text-gray-600 leading-relaxed\"\n                    data-oid=\"o9qh89v\"\n                  >\n                    {crypto.description}\n                  </p>\n                </div>\n              )}\n\n              {/* Mock Chart Placeholder */}\n              <div\n                className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg\"\n                data-oid=\"d7k_qd1\"\n              >\n                <h3\n                  className=\"text-lg font-semibold text-gray-900 mb-4\"\n                  data-oid=\"9jsokkc\"\n                >\n                  Price Chart (7 days)\n                </h3>\n                <div\n                  className=\"h-32 bg-white rounded border-2 border-dashed border-gray-300 flex items-center justify-center\"\n                  data-oid=\"ss.1vtk\"\n                >\n                  <p className=\"text-gray-500\" data-oid=\"0ncg0fk\">\n                    Chart visualization would go here\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div\n              className=\"p-6 border-t bg-gray-50 rounded-b-xl\"\n              data-oid=\".3jogse\"\n            >\n              <div\n                className=\"flex justify-between items-center text-sm text-gray-600\"\n                data-oid=\"osf4ld1\"\n              >\n                <span data-oid=\"wvqq4-h\">Last updated: Just now</span>\n                <span data-oid=\"v07zde6\">Data provided by CryptoBubble</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAZA;;;;AAoBO,MAAM,oBAAsD,CAAC,EAClE,MAAM,EACN,MAAM,EACN,OAAO,EACR;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAC/B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,MAAM,cAAc;IAC7B;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,YAAS;kBACvB,wBACC,6LAAC;YACC,WAAU;YACV,YAAS;;8BAGT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;oBACT,YAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,WAAU;oBACV,YAAS;;sCAGT,6LAAC;4BACC,WAAU;4BACV,YAAS;;8CAET,6LAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAER,OAAO,MAAM,CAAC,MAAM,CAAC;;;;;;sDAExB,6LAAC;4CAAI,YAAS;;8DACZ,6LAAC;oDACC,WAAU;oDACV,YAAS;8DAER,OAAO,IAAI;;;;;;8DAEd,6LAAC;oDAAE,WAAU;oDAAgB,YAAS;;wDACnC,OAAO,MAAM;wDAAC;wDAAU,OAAO,IAAI;;;;;;;;;;;;;;;;;;;8CAI1C,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,YAAS;8CAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;wCAAwB,YAAS;;;;;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;4BAAM,YAAS;;8CAE5B,6LAAC;oCAAI,WAAU;oCAAO,YAAS;;sDAC7B,6LAAC;4CACC,WAAU;4CACV,YAAS;;8DAET,6LAAC;oDACC,WAAU;oDACV,YAAS;;wDACV;wDACG,aAAa,OAAO,KAAK;;;;;;;8DAE7B,6LAAC;oDACC,WAAW,CAAC,+CAA+C,EACzD,OAAO,SAAS,IAAI,IAChB,gCACA,2BACJ;oDACF,YAAS;;wDAER,OAAO,SAAS,IAAI,kBACnB,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;4DAAU,YAAS;;;;;iFAEzC,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;4DAAU,YAAS;;;;;;sEAE7C,6LAAC;4DAAK,WAAU;4DAAgB,YAAS;;gEACtC,OAAO,SAAS,IAAI,IAAI,MAAM;gEAC9B,OAAO,SAAS,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAKnC,6LAAC;4CAAI,WAAU;4CAAwB,YAAS;;gDAAU;8DAExD,6LAAC;oDACC,WAAW,CAAC,mBAAmB,EAC7B,OAAO,QAAQ,IAAI,IAAI,mBAAmB,gBAC1C;oDACF,YAAS;;wDAER,OAAO,QAAQ,IAAI,IAAI,MAAM;wDAC7B,OAAO,QAAQ,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;8CAMlC,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CAAI,WAAU;4CAA4B,YAAS;;8DAClD,6LAAC;oDACC,WAAU;oDACV,YAAS;;sEAET,6LAAC,qNAAA,CAAA,YAAS;4DACR,WAAU;4DACV,YAAS;;;;;;sEAGX,6LAAC;4DACC,WAAU;4DACV,YAAS;sEACV;;;;;;;;;;;;8DAIH,6LAAC;oDACC,WAAU;oDACV,YAAS;8DAER,eAAe,OAAO,SAAS;;;;;;;;;;;;sDAIpC,6LAAC;4CAAI,WAAU;4CAA4B,YAAS;;8DAClD,6LAAC;oDACC,WAAU;oDACV,YAAS;;sEAET,6LAAC,+MAAA,CAAA,UAAO;4DACN,WAAU;4DACV,YAAS;;;;;;sEAGX,6LAAC;4DACC,WAAU;4DACV,YAAS;sEACV;;;;;;;;;;;;8DAIH,6LAAC;oDACC,WAAU;oDACV,YAAS;8DAER,eAAe,OAAO,SAAS;;;;;;;;;;;;sDAIpC,6LAAC;4CAAI,WAAU;4CAA4B,YAAS;;8DAClD,6LAAC;oDACC,WAAU;oDACV,YAAS;;sEAET,6LAAC,qNAAA,CAAA,aAAU;4DACT,WAAU;4DACV,YAAS;;;;;;sEAGX,6LAAC;4DACC,WAAU;4DACV,YAAS;sEACV;;;;;;;;;;;;8DAIH,6LAAC;oDACC,WAAU;oDACV,YAAS;8DAER,OAAO,QAAQ;;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;4CAA4B,YAAS;;8DAClD,6LAAC;oDACC,WAAU;oDACV,YAAS;;sEAET,6LAAC,qNAAA,CAAA,aAAU;4DACT,WAAU;4DACV,YAAS;;;;;;sEAGX,6LAAC;4DACC,WAAU;4DACV,YAAS;sEACV;;;;;;;;;;;;8DAIH,6LAAC;oDACC,WAAU;oDACV,YAAS;;wDAER,CAAC,AAAC,OAAO,SAAS,GAAG,OAAO,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;gCAM/D,OAAO,WAAW,kBACjB,6LAAC;oCAAI,WAAU;oCAAO,YAAS;;sDAC7B,6LAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;sDAGD,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAER,OAAO,WAAW;;;;;;;;;;;;8CAMzB,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;sDAGD,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAET,cAAA,6LAAC;gDAAE,WAAU;gDAAgB,YAAS;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAQtD,6LAAC;4BACC,WAAU;4BACV,YAAS;sCAET,cAAA,6LAAC;gCACC,WAAU;gCACV,YAAS;;kDAET,6LAAC;wCAAK,YAAS;kDAAU;;;;;;kDACzB,6LAAC;wCAAK,YAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;KAhSa", "debugId": null}}, {"offset": {"line": 1692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/data/mockCryptoData.ts"], "sourcesContent": ["export interface CryptoCurrency {\n  id: string;\n  name: string;\n  symbol: string;\n  price: number;\n  marketCap: number;\n  volume24h: number;\n  change24h: number;\n  change7d: number;\n  rank: number;\n  logo?: string;\n  description?: string;\n  website?: string;\n  category: string;\n}\n\nexport const mockCryptoData: CryptoCurrency[] = [\n  {\n    id: \"bitcoin\",\n    name: \"Bitcoin\",\n    symbol: \"BTC\",\n    price: 43250.75,\n    marketCap: 847500000000,\n    volume24h: 28500000000,\n    change24h: 2.45,\n    change7d: -1.23,\n    rank: 1,\n    category: \"Layer 1\",\n    description: \"The first and largest cryptocurrency by market cap\"\n  },\n  {\n    id: \"ethereum\",\n    name: \"Ethereum\",\n    symbol: \"ETH\",\n    price: 2650.30,\n    marketCap: 318750000000,\n    volume24h: 15200000000,\n    change24h: 3.78,\n    change7d: 5.42,\n    rank: 2,\n    category: \"Smart Contract Platform\",\n    description: \"Decentralized platform for smart contracts and DApps\"\n  },\n  {\n    id: \"tether\",\n    name: \"Tether\",\n    symbol: \"USDT\",\n    price: 1.00,\n    marketCap: 91800000000,\n    volume24h: 45600000000,\n    change24h: 0.02,\n    change7d: -0.01,\n    rank: 3,\n    category: \"Stablecoin\",\n    description: \"USD-pegged stablecoin\"\n  },\n  {\n    id: \"bnb\",\n    name: \"BNB\",\n    symbol: \"BNB\",\n    price: 315.80,\n    marketCap: 47370000000,\n    volume24h: 1850000000,\n    change24h: -1.25,\n    change7d: 8.90,\n    rank: 4,\n    category: \"Exchange Token\",\n    description: \"Binance ecosystem token\"\n  },\n  {\n    id: \"solana\",\n    name: \"Solana\",\n    symbol: \"SOL\",\n    price: 98.45,\n    marketCap: 43200000000,\n    volume24h: 2100000000,\n    change24h: 5.67,\n    change7d: 12.34,\n    rank: 5,\n    category: \"Layer 1\",\n    description: \"High-performance blockchain for DeFi and Web3\"\n  },\n  {\n    id: \"usdc\",\n    name: \"USD Coin\",\n    symbol: \"USDC\",\n    price: 1.00,\n    marketCap: 32500000000,\n    volume24h: 5800000000,\n    change24h: 0.01,\n    change7d: 0.00,\n    rank: 6,\n    category: \"Stablecoin\",\n    description: \"Regulated USD-backed stablecoin\"\n  },\n  {\n    id: \"xrp\",\n    name: \"XRP\",\n    symbol: \"XRP\",\n    price: 0.62,\n    marketCap: 33800000000,\n    volume24h: 1200000000,\n    change24h: -2.15,\n    change7d: -5.67,\n    rank: 7,\n    category: \"Payment\",\n    description: \"Digital payment protocol for financial institutions\"\n  },\n  {\n    id: \"cardano\",\n    name: \"Cardano\",\n    symbol: \"ADA\",\n    price: 0.48,\n    marketCap: 16900000000,\n    volume24h: 450000000,\n    change24h: 1.89,\n    change7d: 3.45,\n    rank: 8,\n    category: \"Layer 1\",\n    description: \"Proof-of-stake blockchain platform\"\n  },\n  {\n    id: \"avalanche\",\n    name: \"Avalanche\",\n    symbol: \"AVAX\",\n    price: 36.75,\n    marketCap: 14200000000,\n    volume24h: 680000000,\n    change24h: 4.23,\n    change7d: 15.67,\n    rank: 9,\n    category: \"Layer 1\",\n    description: \"Platform for decentralized applications and custom blockchain networks\"\n  },\n  {\n    id: \"dogecoin\",\n    name: \"Dogecoin\",\n    symbol: \"DOGE\",\n    price: 0.085,\n    marketCap: 12100000000,\n    volume24h: 890000000,\n    change24h: -3.45,\n    change7d: 2.10,\n    rank: 10,\n    category: \"Meme\",\n    description: \"The original meme cryptocurrency\"\n  },\n  {\n    id: \"chainlink\",\n    name: \"Chainlink\",\n    symbol: \"LINK\",\n    price: 14.85,\n    marketCap: 8750000000,\n    volume24h: 420000000,\n    change24h: 2.67,\n    change7d: 8.90,\n    rank: 11,\n    category: \"Oracle\",\n    description: \"Decentralized oracle network\"\n  },\n  {\n    id: \"polygon\",\n    name: \"Polygon\",\n    symbol: \"MATIC\",\n    price: 0.89,\n    marketCap: 8200000000,\n    volume24h: 380000000,\n    change24h: 6.78,\n    change7d: 18.45,\n    rank: 12,\n    category: \"Layer 2\",\n    description: \"Ethereum scaling solution\"\n  },\n  {\n    id: \"litecoin\",\n    name: \"Litecoin\",\n    symbol: \"LTC\",\n    price: 72.30,\n    marketCap: 5400000000,\n    volume24h: 320000000,\n    change24h: -1.56,\n    change7d: 4.23,\n    rank: 13,\n    category: \"Payment\",\n    description: \"Peer-to-peer cryptocurrency based on Bitcoin\"\n  },\n  {\n    id: \"uniswap\",\n    name: \"Uniswap\",\n    symbol: \"UNI\",\n    price: 6.45,\n    marketCap: 4850000000,\n    volume24h: 180000000,\n    change24h: 3.89,\n    change7d: 12.67,\n    rank: 14,\n    category: \"DeFi\",\n    description: \"Decentralized exchange protocol\"\n  },\n  {\n    id: \"internet-computer\",\n    name: \"Internet Computer\",\n    symbol: \"ICP\",\n    price: 12.75,\n    marketCap: 5900000000,\n    volume24h: 95000000,\n    change24h: -2.34,\n    change7d: 7.89,\n    rank: 15,\n    category: \"Layer 1\",\n    description: \"Blockchain computer that scales smart contract computation\"\n  }\n];\n\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n  private seed: number;\n\n  constructor(seed: number) {\n    this.seed = seed;\n  }\n\n  next(): number {\n    this.seed = (this.seed * 9301 + 49297) % 233280;\n    return this.seed / 233280;\n  }\n}\n\n// Helper function to generate additional random crypto data with deterministic values\nexport const generateRandomCrypto = (count: number): CryptoCurrency[] => {\n  const categories = [\"DeFi\", \"Layer 1\", \"Layer 2\", \"Meme\", \"Gaming\", \"NFT\", \"Oracle\", \"Privacy\", \"Storage\"];\n  const cryptoNames = [\n    \"ApeCoin\", \"Shiba Inu\", \"Cosmos\", \"Algorand\", \"VeChain\", \"Filecoin\", \"Sandbox\",\n    \"Decentraland\", \"Axie Infinity\", \"Theta\", \"Hedera\", \"Elrond\", \"Near Protocol\",\n    \"Flow\", \"Tezos\", \"Fantom\", \"Harmony\", \"Zilliqa\", \"Enjin\", \"Basic Attention Token\",\n    \"Compound\", \"Maker\", \"Aave\", \"Curve\", \"SushiSwap\", \"PancakeSwap\", \"1inch\",\n    \"Yearn Finance\", \"Synthetix\", \"Balancer\", \"Bancor\", \"Kyber Network\", \"0x Protocol\"\n  ];\n\n  // Use a fixed seed for consistent results across server and client\n  const rng = new SeededRandom(12345);\n\n  return Array.from({ length: count }, (_, i) => {\n    const basePrice = rng.next() * 1000 + 0.01;\n    const marketCap = basePrice * (rng.next() * 1000000000 + 1000000);\n\n    return {\n      id: `crypto-${i + 16}`,\n      name: cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`,\n      symbol: `C${i + 16}`,\n      price: Number(basePrice.toFixed(4)),\n      marketCap: Number(marketCap.toFixed(0)),\n      volume24h: Number((marketCap * (rng.next() * 0.3 + 0.05)).toFixed(0)),\n      change24h: Number(((rng.next() - 0.5) * 20).toFixed(2)),\n      change7d: Number(((rng.next() - 0.5) * 40).toFixed(2)),\n      rank: i + 16,\n      category: categories[Math.floor(rng.next() * categories.length)],\n      description: `Description for ${cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`}`\n    };\n  });\n};\n\n// Combine static data with generated data for a total of 50+ cryptocurrencies\nexport const getAllCryptoData = (): CryptoCurrency[] => {\n  return [...mockCryptoData, ...generateRandomCrypto(35)];\n};\n"], "names": [], "mappings": ";;;;;AAgBO,MAAM,iBAAmC;IAC9C;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;CACD;AAED,+DAA+D;AAC/D,MAAM;IACI,KAAa;IAErB,YAAY,IAAY,CAAE;QACxB,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAe;QACb,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,KAAK,IAAI;QACzC,OAAO,IAAI,CAAC,IAAI,GAAG;IACrB;AACF;AAGO,MAAM,uBAAuB,CAAC;IACnC,MAAM,aAAa;QAAC;QAAQ;QAAW;QAAW;QAAQ;QAAU;QAAO;QAAU;QAAW;KAAU;IAC1G,MAAM,cAAc;QAClB;QAAW;QAAa;QAAU;QAAY;QAAW;QAAY;QACrE;QAAgB;QAAiB;QAAS;QAAU;QAAU;QAC9D;QAAQ;QAAS;QAAU;QAAW;QAAW;QAAS;QAC1D;QAAY;QAAS;QAAQ;QAAS;QAAa;QAAe;QAClE;QAAiB;QAAa;QAAY;QAAU;QAAiB;KACtE;IAED,mEAAmE;IACnE,MAAM,MAAM,IAAI,aAAa;IAE7B,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG;QACvC,MAAM,YAAY,IAAI,IAAI,KAAK,OAAO;QACtC,MAAM,YAAY,YAAY,CAAC,IAAI,IAAI,KAAK,aAAa,OAAO;QAEhE,OAAO;YACL,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YACtB,MAAM,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YAC/D,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI;YACpB,OAAO,OAAO,UAAU,OAAO,CAAC;YAChC,WAAW,OAAO,UAAU,OAAO,CAAC;YACpC,WAAW,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;YAClE,WAAW,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACpD,UAAU,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACnD,MAAM,IAAI;YACV,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,WAAW,MAAM,EAAE;YAChE,aAAa,CAAC,gBAAgB,EAAE,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE;QAC7F;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,OAAO;WAAI;WAAmB,qBAAqB;KAAI;AACzD", "debugId": null}}, {"offset": {"line": 1989, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useMemo, useEffect } from \"react\";\nimport { ResponsiveBubbleChart } from \"@/components/ResponsiveBubbleChart\";\nimport { SearchAndFilter } from \"@/components/SearchAndFilter\";\nimport { CryptoDetailModal } from \"@/components/CryptoDetailModal\";\nimport { getAllCryptoData } from \"@/data/mockCryptoData\";\nimport { CryptoCurrency, FilterOptions } from \"@/types\";\n\nexport default function Home() {\n  const [selectedCrypto, setSelectedCrypto] = useState<CryptoCurrency | null>(\n    null,\n  );\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [filters, setFilters] = useState<FilterOptions>({\n    sortBy: \"marketCap\",\n    sortOrder: \"desc\",\n  });\n\n  const allCryptoData = getAllCryptoData();\n\n  // Simulate loading for demo purposes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Get unique categories for filter dropdown\n  const categories = useMemo(() => {\n    const uniqueCategories = [\n      ...new Set(allCryptoData.map((crypto) => crypto.category)),\n    ];\n\n    return uniqueCategories.sort();\n  }, [allCryptoData]);\n\n  // Filter and sort data\n  const filteredData = useMemo(() => {\n    let filtered = allCryptoData;\n\n    // Apply search filter\n    if (filters.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(\n        (crypto) =>\n          crypto.name.toLowerCase().includes(searchLower) ||\n          crypto.symbol.toLowerCase().includes(searchLower),\n      );\n    }\n\n    // Apply category filter\n    if (filters.category) {\n      filtered = filtered.filter(\n        (crypto) => crypto.category === filters.category,\n      );\n    }\n\n    // Apply sorting\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        const aValue = a[filters.sortBy!];\n        const bValue = b[filters.sortBy!];\n\n        if (typeof aValue === \"string\" && typeof bValue === \"string\") {\n          return filters.sortOrder === \"desc\"\n            ? bValue.localeCompare(aValue)\n            : aValue.localeCompare(bValue);\n        }\n\n        return filters.sortOrder === \"desc\"\n          ? (bValue as number) - (aValue as number)\n          : (aValue as number) - (bValue as number);\n      });\n    }\n\n    return filtered;\n  }, [allCryptoData, filters]);\n\n  const handleBubbleClick = (crypto: CryptoCurrency) => {\n    setSelectedCrypto(crypto);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedCrypto(null);\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\" data-oid=\"nh6aeyy\">\n      {/* Full Screen Bubble Chart */}\n      <ResponsiveBubbleChart\n        data={filteredData}\n        isLoading={isLoading}\n        data-oid=\"e1qxf3h\"\n      />\n\n      {/* Floating Search and Filter Controls */}\n      <div className=\"fixed top-4 left-4 z-50\" data-oid=\"vkdbvt_\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"eymeu:z\"\n        >\n          <SearchAndFilter\n            filters={filters}\n            onFiltersChange={setFilters}\n            categories={categories}\n            compact={true}\n            data-oid=\"h7iy:g:\"\n          />\n        </div>\n      </div>\n\n      {/* Floating Stats Panel */}\n      <div className=\"fixed bottom-4 right-4 z-50\" data-oid=\"eq-diib\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30 min-w-64\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"ohdqnrj\"\n        >\n          <h3 className=\"font-semibold text-white mb-3\" data-oid=\"n5d9o79\">\n            Market Stats\n          </h3>\n          <div className=\"grid grid-cols-2 gap-3 text-sm\" data-oid=\"_l3_99u\">\n            <div\n              className=\"text-center p-2 bg-green-50 rounded\"\n              data-oid=\"9e3:ppv\"\n            >\n              <div\n                className=\"text-lg font-bold text-green-700\"\n                data-oid=\"i30-cwk\"\n              >\n                {filteredData.filter((c) => c.change24h > 0).length}\n              </div>\n              <div\n                className=\"text-xs text-white font-medium\"\n                data-oid=\"rowh_on\"\n              >\n                Gainers\n              </div>\n            </div>\n            <div\n              className=\"text-center p-2 bg-red-50 rounded\"\n              data-oid=\"x51:-qe\"\n            >\n              <div\n                className=\"text-lg font-bold text-red-700\"\n                data-oid=\"sdgg:_:\"\n              >\n                {filteredData.filter((c) => c.change24h < 0).length}\n              </div>\n              <div\n                className=\"text-xs text-white font-medium\"\n                data-oid=\"zs5q0d4\"\n              >\n                Losers\n              </div>\n            </div>\n          </div>\n          <div\n            className=\"mt-3 text-xs text-white font-medium\"\n            data-oid=\"_c1ulh4\"\n          >\n            <div data-oid=\"9pw2hq4\">\n              Total: {filteredData.length} cryptocurrencies\n            </div>\n            <div data-oid=\"xwn9wh8\">\n              Market Cap: $\n              {(\n                filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12\n              ).toFixed(2)}\n              T\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Legend */}\n      <div className=\"fixed bottom-4 left-4 z-50\" data-oid=\"u:28mnw\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"p-ize95\"\n        >\n          <h3\n            className=\"font-semibold text-white mb-3 text-sm\"\n            data-oid=\"66k_ne3\"\n          >\n            Legend\n          </h3>\n          <div className=\"space-y-2 text-xs\" data-oid=\"augmved\">\n            <div className=\"flex items-center gap-2\" data-oid=\"dhb_z-n\">\n              <div\n                className=\"w-3 h-3 bg-green-500 rounded-full\"\n                data-oid=\"my0pfm:\"\n              ></div>\n              <span className=\"text-white\" data-oid=\"m9sb8oj\">\n                Price increase (24h)\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2\" data-oid=\"1uvbjf1\">\n              <div\n                className=\"w-3 h-3 bg-red-500 rounded-full\"\n                data-oid=\"7bwgens\"\n              ></div>\n              <span className=\"text-white\" data-oid=\"lzch3fp\">\n                Price decrease (24h)\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2\" data-oid=\"gnf8q4h\">\n              <div\n                className=\"w-4 h-4 bg-gray-400 rounded-full\"\n                data-oid=\"r9nv:ru\"\n              ></div>\n              <span className=\"text-white\" data-oid=\".sd-nlo\">\n                Bubble size = Market cap\n              </span>\n            </div>\n            <div\n              className=\"pt-2 border-t border-white/30 text-xs text-white\"\n              data-oid=\"3.9phi-\"\n            >\n              <div data-oid=\"298.kho\">• Hover bubbles for details</div>\n              <div data-oid=\"bmts3ij\">• Click bubbles for more info</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detail Modal */}\n      <CryptoDetailModal\n        crypto={selectedCrypto}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n        data-oid=\"85t4zus\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAErC,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ;wCAAW;oBACvB,aAAa;gBACf;uCAAG;YAEH;kCAAO,IAAM,aAAa;;QAC5B;yBAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE;YACzB,MAAM,mBAAmB;mBACpB,IAAI,IAAI,cAAc,GAAG;gDAAC,CAAC,SAAW,OAAO,QAAQ;;aACzD;YAED,OAAO,iBAAiB,IAAI;QAC9B;mCAAG;QAAC;KAAc;IAElB,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE;YAC3B,IAAI,WAAW;YAEf,sBAAsB;YACtB,IAAI,QAAQ,UAAU,EAAE;gBACtB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;gBAClD,WAAW,SAAS,MAAM;kDACxB,CAAC,SACC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAE3C;YAEA,wBAAwB;YACxB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,SAAS,MAAM;kDACxB,CAAC,SAAW,OAAO,QAAQ,KAAK,QAAQ,QAAQ;;YAEpD;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,EAAE;gBAClB,SAAS,IAAI;kDAAC,CAAC,GAAG;wBAChB,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;wBACjC,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;wBAEjC,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;4BAC5D,OAAO,QAAQ,SAAS,KAAK,SACzB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;wBAC3B;wBAEA,OAAO,QAAQ,SAAS,KAAK,SACzB,AAAC,SAAqB,SACtB,AAAC,SAAqB;oBAC5B;;YACF;YAEA,OAAO;QACT;qCAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAkC,YAAS;;0BAExD,6LAAC,8IAAA,CAAA,wBAAqB;gBACpB,MAAM;gBACN,WAAW;gBACX,YAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;gBAA0B,YAAS;0BAChD,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;8BAET,cAAA,6LAAC,wIAAA,CAAA,kBAAe;wBACd,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,SAAS;wBACT,YAAS;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAI,WAAU;gBAA8B,YAAS;0BACpD,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;;sCAET,6LAAC;4BAAG,WAAU;4BAAgC,YAAS;sCAAU;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;4BAAiC,YAAS;;8CACvD,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAER,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAErD,6LAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;;;;;;;8CAIH,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CACC,WAAU;4CACV,YAAS;sDAER,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAErD,6LAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;;;;;;;;;;;;;sCAKL,6LAAC;4BACC,WAAU;4BACV,YAAS;;8CAET,6LAAC;oCAAI,YAAS;;wCAAU;wCACd,aAAa,MAAM;wCAAC;;;;;;;8CAE9B,6LAAC;oCAAI,YAAS;;wCAAU;wCAErB,CACC,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,IAC1D,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,6LAAC;gBAAI,WAAU;gBAA6B,YAAS;0BACnD,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;;sCAET,6LAAC;4BACC,WAAU;4BACV,YAAS;sCACV;;;;;;sCAGD,6LAAC;4BAAI,WAAU;4BAAoB,YAAS;;8CAC1C,6LAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,6LAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,6LAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,6LAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,6LAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,6LAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,6LAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,6LAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,6LAAC;4CAAI,YAAS;sDAAU;;;;;;sDACxB,6LAAC;4CAAI,YAAS;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,6LAAC,0IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,YAAS;;;;;;;;;;;;AAIjB;GAnPwB;KAAA", "debugId": null}}]}