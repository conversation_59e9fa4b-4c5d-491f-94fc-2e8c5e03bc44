export interface CryptoCurrency {
  id: string;
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  volume24h: number;
  change24h: number;
  change7d: number;
  rank: number;
  logo?: string;
  description?: string;
  website?: string;
  category: string;
}

export const mockCryptoData: CryptoCurrency[] = [
  {
    id: "bitcoin",
    name: "Bitcoin",
    symbol: "BTC",
    price: 43250.75,
    marketCap: 847500000000,
    volume24h: 28500000000,
    change24h: 2.45,
    change7d: -1.23,
    rank: 1,
    category: "Layer 1",
    description: "The first and largest cryptocurrency by market cap",
    logo: "https://assets.coingecko.com/coins/images/1/large/bitcoin.png"
  },
  {
    id: "ethereum",
    name: "Ethereum",
    symbol: "ETH",
    price: 2650.30,
    marketCap: 318750000000,
    volume24h: 15200000000,
    change24h: 3.78,
    change7d: 5.42,
    rank: 2,
    category: "Smart Contract Platform",
    description: "Decentralized platform for smart contracts and DApps",
    logo: "https://assets.coingecko.com/coins/images/279/large/ethereum.png"
  },
  {
    id: "tether",
    name: "Tether",
    symbol: "USDT",
    price: 1.00,
    marketCap: 91800000000,
    volume24h: 45600000000,
    change24h: 0.02,
    change7d: -0.01,
    rank: 3,
    category: "Stablecoin",
    description: "USD-pegged stablecoin",
    logo: "https://assets.coingecko.com/coins/images/325/large/Tether.png"
  },
  {
    id: "bnb",
    name: "BNB",
    symbol: "BNB",
    price: 315.80,
    marketCap: 47370000000,
    volume24h: 1850000000,
    change24h: -1.25,
    change7d: 8.90,
    rank: 4,
    category: "Exchange Token",
    description: "Binance ecosystem token",
    logo: "https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png"
  },
  {
    id: "solana",
    name: "Solana",
    symbol: "SOL",
    price: 98.45,
    marketCap: 43200000000,
    volume24h: 2100000000,
    change24h: 5.67,
    change7d: 12.34,
    rank: 5,
    category: "Layer 1",
    description: "High-performance blockchain for DeFi and Web3",
    logo: "https://assets.coingecko.com/coins/images/4128/large/solana.png"
  },
  {
    id: "usdc",
    name: "USD Coin",
    symbol: "USDC",
    price: 1.00,
    marketCap: 32500000000,
    volume24h: 5800000000,
    change24h: 0.01,
    change7d: 0.00,
    rank: 6,
    category: "Stablecoin",
    description: "Regulated USD-backed stablecoin",
    logo: "https://assets.coingecko.com/coins/images/6319/large/USD_Coin_icon.png"
  },
  {
    id: "xrp",
    name: "XRP",
    symbol: "XRP",
    price: 0.62,
    marketCap: 33800000000,
    volume24h: 1200000000,
    change24h: -2.15,
    change7d: -5.67,
    rank: 7,
    category: "Payment",
    description: "Digital payment protocol for financial institutions",
    logo: "https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png"
  },
  {
    id: "cardano",
    name: "Cardano",
    symbol: "ADA",
    price: 0.48,
    marketCap: 16900000000,
    volume24h: 450000000,
    change24h: 1.89,
    change7d: 3.45,
    rank: 8,
    category: "Layer 1",
    description: "Proof-of-stake blockchain platform",
    logo: "https://assets.coingecko.com/coins/images/975/large/cardano.png"
  },
  {
    id: "avalanche",
    name: "Avalanche",
    symbol: "AVAX",
    price: 36.75,
    marketCap: 14200000000,
    volume24h: 680000000,
    change24h: 4.23,
    change7d: 15.67,
    rank: 9,
    category: "Layer 1",
    description: "Platform for decentralized applications and custom blockchain networks",
    logo: "https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png"
  },
  {
    id: "dogecoin",
    name: "Dogecoin",
    symbol: "DOGE",
    price: 0.085,
    marketCap: 12100000000,
    volume24h: 890000000,
    change24h: -3.45,
    change7d: 2.10,
    rank: 10,
    category: "Meme",
    description: "The original meme cryptocurrency",
    logo: "https://assets.coingecko.com/coins/images/5/large/dogecoin.png"
  },
  {
    id: "chainlink",
    name: "Chainlink",
    symbol: "LINK",
    price: 14.85,
    marketCap: 8750000000,
    volume24h: 420000000,
    change24h: 2.67,
    change7d: 8.90,
    rank: 11,
    category: "Oracle",
    description: "Decentralized oracle network"
  },
  {
    id: "polygon",
    name: "Polygon",
    symbol: "MATIC",
    price: 0.89,
    marketCap: 8200000000,
    volume24h: 380000000,
    change24h: 6.78,
    change7d: 18.45,
    rank: 12,
    category: "Layer 2",
    description: "Ethereum scaling solution"
  },
  {
    id: "litecoin",
    name: "Litecoin",
    symbol: "LTC",
    price: 72.30,
    marketCap: 5400000000,
    volume24h: 320000000,
    change24h: -1.56,
    change7d: 4.23,
    rank: 13,
    category: "Payment",
    description: "Peer-to-peer cryptocurrency based on Bitcoin"
  },
  {
    id: "uniswap",
    name: "Uniswap",
    symbol: "UNI",
    price: 6.45,
    marketCap: 4850000000,
    volume24h: 180000000,
    change24h: 3.89,
    change7d: 12.67,
    rank: 14,
    category: "DeFi",
    description: "Decentralized exchange protocol"
  },
  {
    id: "internet-computer",
    name: "Internet Computer",
    symbol: "ICP",
    price: 12.75,
    marketCap: 5900000000,
    volume24h: 95000000,
    change24h: -2.34,
    change7d: 7.89,
    rank: 15,
    category: "Layer 1",
    description: "Blockchain computer that scales smart contract computation"
  }
];

// Simple seeded random number generator for consistent results
class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }
}

// Helper function to generate additional random crypto data with deterministic values
export const generateRandomCrypto = (count: number): CryptoCurrency[] => {
  const categories = ["DeFi", "Layer 1", "Layer 2", "Meme", "Gaming", "NFT", "Oracle", "Privacy", "Storage"];
  const cryptoNames = [
    "ApeCoin", "Shiba Inu", "Cosmos", "Algorand", "VeChain", "Filecoin", "Sandbox",
    "Decentraland", "Axie Infinity", "Theta", "Hedera", "Elrond", "Near Protocol",
    "Flow", "Tezos", "Fantom", "Harmony", "Zilliqa", "Enjin", "Basic Attention Token",
    "Compound", "Maker", "Aave", "Curve", "SushiSwap", "PancakeSwap", "1inch",
    "Yearn Finance", "Synthetix", "Balancer", "Bancor", "Kyber Network", "0x Protocol"
  ];

  // Use a fixed seed for consistent results across server and client
  const rng = new SeededRandom(12345);

  return Array.from({ length: count }, (_, i) => {
    const basePrice = rng.next() * 1000 + 0.01;
    const marketCap = basePrice * (rng.next() * 1000000000 + 1000000);

    return {
      id: `crypto-${i + 16}`,
      name: cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`,
      symbol: `C${i + 16}`,
      price: Number(basePrice.toFixed(4)),
      marketCap: Number(marketCap.toFixed(0)),
      volume24h: Number((marketCap * (rng.next() * 0.3 + 0.05)).toFixed(0)),
      change24h: Number(((rng.next() - 0.5) * 20).toFixed(2)),
      change7d: Number(((rng.next() - 0.5) * 40).toFixed(2)),
      rank: i + 16,
      category: categories[Math.floor(rng.next() * categories.length)],
      description: `Description for ${cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`}`
    };
  });
};

// Combine static data with generated data for a total of 50+ cryptocurrencies
export const getAllCryptoData = (): CryptoCurrency[] => {
  return [...mockCryptoData, ...generateRandomCrypto(35)];
};
